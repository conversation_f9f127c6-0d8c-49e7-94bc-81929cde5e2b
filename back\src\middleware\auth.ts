import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { executeQuery, redis } from '../config/database';
import { logger, logAuthEvent, logSecurityEvent } from '../config/logger';
import { settingsService } from '../services/settingsService';
// Local type definitions
export interface User {
  id: string;
  email: string;
  telegramId: string;
  emailVerified: boolean;
  role: 'user' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: 'user' | 'admin';
  iat: number;
  exp: number;
}

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// JWT token generation
export const generateTokens = (user: User): { accessToken: string; refreshToken: string } => {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    role: user.role,
  };

  const accessToken = jwt.sign(
    payload,
    process.env.JWT_SECRET!,
    { expiresIn: '15m' }
  );

  const refreshToken = jwt.sign(
    payload,
    process.env.JWT_SECRET!,
    { expiresIn: '7d' }
  );

  return { accessToken, refreshToken };
};

// Verify JWT token
export const verifyToken = (token: string): JWTPayload | null => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
  } catch (error) {
    return null;
  }
};

// Authentication middleware
export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access token required',
      });
    }

    const token = authHeader.substring(7);
    
    // Check if token is blacklisted
    const isBlacklisted = await redis.get(`blacklist:${token}`);
    if (isBlacklisted) {
      logSecurityEvent('BLACKLISTED_TOKEN_USED', { token: token.substring(0, 10) + '...' }, req);
      return res.status(401).json({
        success: false,
        error: 'Token is invalid',
      });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
      });
    }

    // Get user from database
    const userResult = await executeQuery(
      'SELECT id, email, telegram_id, email_verified, role, created_at, updated_at FROM users WHERE id = $1 AND role IS NOT NULL',
      [payload.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'User not found',
      });
    }

    const user: User = {
      id: userResult.rows[0].id,
      email: userResult.rows[0].email,
      telegramId: userResult.rows[0].telegram_id,
      emailVerified: userResult.rows[0].email_verified,
      role: userResult.rows[0].role,
      createdAt: userResult.rows[0].created_at,
      updatedAt: userResult.rows[0].updated_at,
    };

    req.user = user;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication failed',
    });
  }
};

// Authorization middleware
export const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
    }

    if (!roles.includes(req.user.role)) {
      logSecurityEvent('UNAUTHORIZED_ACCESS_ATTEMPT', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        path: req.path,
      }, req);
      
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
      });
    }

    next();
  };
};

// Admin authorization
export const requireAdmin = authorize(['admin']);

// User authorization (both user and admin)
export const requireUser = authorize(['user', 'admin']);

// Password hashing
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  return bcrypt.hash(password, saltRounds);
};

// Password verification
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

// Account lockout middleware
export const checkAccountLockout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return next();
    }

    const userResult = await executeQuery(
      'SELECT failed_login_attempts, locked_until FROM users WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      return next();
    }

    const user = userResult.rows[0];
    const now = new Date();
    
    // Check if account is currently locked
    if (user.locked_until && new Date(user.locked_until) > now) {
      const lockoutRemaining = Math.ceil((new Date(user.locked_until).getTime() - now.getTime()) / 1000 / 60);
      
      logSecurityEvent('LOCKED_ACCOUNT_ACCESS_ATTEMPT', {
        email,
        lockoutRemaining,
      }, req);
      
      return res.status(423).json({
        success: false,
        error: `Account is locked. Try again in ${lockoutRemaining} minutes.`,
      });
    }

    // Reset failed attempts if lockout period has expired
    if (user.locked_until && new Date(user.locked_until) <= now) {
      await executeQuery(
        'UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE email = $1',
        [email]
      );
    }

    next();
  } catch (error) {
    logger.error('Account lockout check error:', error);
    next();
  }
};

// Handle failed login attempt with dynamic security settings
export const handleFailedLogin = async (email: string, req: Request) => {
  try {
    // Get security settings from database
    const securitySettings = await settingsService.getSecuritySettings();
    const maxAttempts = securitySettings.maxLoginAttempts;
    const lockoutDuration = securitySettings.lockoutDurationMinutes * 60 * 1000; // Convert minutes to milliseconds

    const result = await executeQuery(
      'UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE email = $1 RETURNING failed_login_attempts',
      [email]
    );

    if (result.rows.length > 0) {
      const attempts = result.rows[0].failed_login_attempts;

      if (attempts >= maxAttempts) {
        const lockoutUntil = new Date(Date.now() + lockoutDuration);

        await executeQuery(
          'UPDATE users SET locked_until = $1 WHERE email = $2',
          [lockoutUntil, email]
        );

        logSecurityEvent('ACCOUNT_LOCKED', {
          email,
          attempts,
          lockoutUntil,
          maxAttempts,
          lockoutDurationMinutes: securitySettings.lockoutDurationMinutes,
        }, req);
      }

      logAuthEvent('LOGIN_FAILED', email, false, req);
    }
  } catch (error) {
    logger.error('Failed login handling error:', error);
    // Fallback to environment variables if settings service fails
    const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5');
    const lockoutDuration = parseInt(process.env.LOCKOUT_DURATION || '1800000');

    const result = await executeQuery(
      'UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE email = $1 RETURNING failed_login_attempts',
      [email]
    );

    if (result.rows.length > 0) {
      const attempts = result.rows[0].failed_login_attempts;

      if (attempts >= maxAttempts) {
        const lockoutUntil = new Date(Date.now() + lockoutDuration);

        await executeQuery(
          'UPDATE users SET locked_until = $1 WHERE email = $2',
          [lockoutUntil, email]
        );

        logSecurityEvent('ACCOUNT_LOCKED', {
          email,
          attempts,
          lockoutUntil,
        }, req);
      }

      logAuthEvent('LOGIN_FAILED', email, false, req);
    }
  }
};

// Handle successful login
export const handleSuccessfulLogin = async (email: string, req: Request) => {
  try {
    await executeQuery(
      'UPDATE users SET failed_login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP WHERE email = $1',
      [email]
    );
    
    logAuthEvent('LOGIN_SUCCESS', email, true, req);
  } catch (error) {
    logger.error('Successful login handling error:', error);
  }
};

// Blacklist token (for logout)
export const blacklistToken = async (token: string, expiresIn: number) => {
  try {
    await redis.setEx(`blacklist:${token}`, expiresIn, 'true');
  } catch (error) {
    logger.error('Token blacklisting error:', error);
  }
};

// Session management with dynamic timeout
export const createUserSession = async (userId: string, sessionToken: string, refreshToken: string, req: Request) => {
  try {
    // Get session timeout from security settings
    const securitySettings = await settingsService.getSecuritySettings();
    const sessionTimeoutMs = securitySettings.sessionTimeoutMinutes * 60 * 1000; // Convert minutes to milliseconds
    const expiresAt = new Date(Date.now() + sessionTimeoutMs);

    await executeQuery(
      `INSERT INTO user_sessions (user_id, session_token, refresh_token, expires_at, ip_address, user_agent)
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        userId,
        sessionToken,
        refreshToken,
        expiresAt,
        req.ip,
        req.get('User-Agent'),
      ]
    );

    logger.debug('User session created with dynamic timeout', {
      userId,
      sessionTimeoutMinutes: securitySettings.sessionTimeoutMinutes,
      expiresAt
    });
  } catch (error) {
    logger.error('Session creation error:', error);
    // Fallback to default 7 days if settings service fails
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    await executeQuery(
      `INSERT INTO user_sessions (user_id, session_token, refresh_token, expires_at, ip_address, user_agent)
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        userId,
        sessionToken,
        refreshToken,
        expiresAt,
        req.ip,
        req.get('User-Agent'),
      ]
    );
  }
};

// Cleanup expired sessions
export const cleanupExpiredSessions = async () => {
  try {
    const result = await executeQuery(
      'DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP'
    );
    
    if (result.rowCount > 0) {
      logger.info(`Cleaned up ${result.rowCount} expired sessions`);
    }
  } catch (error) {
    logger.error('Session cleanup error:', error);
  }
};

// Validate session
export const validateSession = async (sessionToken: string): Promise<boolean> => {
  try {
    const result = await executeQuery(
      'SELECT id FROM user_sessions WHERE session_token = $1 AND expires_at > CURRENT_TIMESTAMP AND is_active = true',
      [sessionToken]
    );
    
    return result.rows.length > 0;
  } catch (error) {
    logger.error('Session validation error:', error);
    return false;
  }
};

// Invalidate session
export const invalidateSession = async (sessionToken: string) => {
  try {
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE session_token = $1',
      [sessionToken]
    );
  } catch (error) {
    logger.error('Session invalidation error:', error);
  }
};
