import { Router } from 'express';
import {
  register,
  login,
  logout,
  verifyEmail,
  resendVerification,
  requestPasswordReset,
  resetPassword,
  refreshToken,
} from '../controllers/authController';
import {
  validateInput,
  emailValidation,
  passwordValidation,
  createDynamicPasswordValidation,
  telegramIdValidation,
  memoValidation,
  authRateLimit,
  createDynamicAuthRateLimit,
  userAuthRateLimit,
} from '../middleware/security';
import { authenticate, checkAccountLockout } from '../middleware/auth';
import { body } from 'express-validator';

const router = Router();

// Create dynamic validation middleware
const createDynamicRegisterValidation = async () => {
  const dynamicPasswordValidation = await createDynamicPasswordValidation();
  return [
    emailValidation,
    telegramIdValidation,
    dynamicPasswordValidation,
    memoValidation,
  ];
};

// Create dynamic auth rate limit
const createDynamicAuthMiddleware = async () => {
  return await createDynamicAuthRateLimit();
};

// Static validation (fallback)
const registerValidation = [
  emailValidation,
  telegramIdValidation,
  passwordValidation,
  memoValidation,
];

// Login validation
const loginValidation = [
  emailValidation,
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

// Email verification validation
const verifyEmailValidation = [
  body('token')
    .isLength({ min: 32, max: 128 })
    .withMessage('Invalid verification token format'),
];

// Password reset request validation
const passwordResetRequestValidation = [
  emailValidation,
];

// Password reset validation
const passwordResetValidation = [
  body('token')
    .isLength({ min: 32, max: 128 })
    .withMessage('Invalid reset token format'),
  passwordValidation,
];

// Refresh token validation
const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
];

// Routes

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post(
  '/register',
  authRateLimit, // IP-based rate limiting
  userAuthRateLimit, // Per-user rate limiting
  validateInput(registerValidation),
  register
);

/**
 * @route   POST /api/v1/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post(
  '/login',
  authRateLimit, // IP-based rate limiting
  userAuthRateLimit, // Per-user rate limiting
  checkAccountLockout,
  validateInput(loginValidation),
  login
);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post(
  '/logout',
  authenticate,
  logout
);

/**
 * @route   POST /api/v1/auth/verify-email
 * @desc    Verify user email address
 * @access  Public
 */
router.post(
  '/verify-email',
  validateInput(verifyEmailValidation),
  verifyEmail
);

/**
 * @route   POST /api/v1/auth/resend-verification
 * @desc    Resend email verification
 * @access  Public
 */
router.post(
  '/resend-verification',
  authRateLimit,
  validateInput([emailValidation]),
  resendVerification
);

/**
 * @route   POST /api/v1/auth/request-password-reset
 * @desc    Request password reset
 * @access  Public
 */
router.post(
  '/request-password-reset',
  authRateLimit,
  validateInput(passwordResetRequestValidation),
  requestPasswordReset
);

/**
 * @route   POST /api/v1/auth/reset-password
 * @desc    Reset password with token
 * @access  Public
 */
router.post(
  '/reset-password',
  authRateLimit,
  validateInput(passwordResetValidation),
  resetPassword
);

/**
 * @route   POST /api/v1/auth/refresh-token
 * @desc    Refresh access token
 * @access  Public
 */
router.post(
  '/refresh-token',
  validateInput(refreshTokenValidation),
  refreshToken
);

/**
 * @route   GET /api/v1/auth/me
 * @desc    Get current user info
 * @access  Private
 */
router.get(
  '/me',
  authenticate,
  (req, res) => {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user!.id,
          email: req.user!.email,
          telegramId: req.user!.telegramId,
          emailVerified: req.user!.emailVerified,
          role: req.user!.role,
          createdAt: req.user!.createdAt,
          updatedAt: req.user!.updatedAt,
        },
      },
    });
  }
);

export default router;
