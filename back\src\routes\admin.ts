import { Router } from 'express';
import {
  getAdminStats,
  getUsers,
  updateUser,
  getOrders,
  getVouchers,
  bulkCreateVouchers,
  createVoucher,
  updateVoucherStatus,
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductCategories,
  createProductCategory,
} from '../controllers/adminController';
import {
  getSystemSettings,
  updateSystemSettings,
  getSettingsHistory,
  resetSettingsToDefault,
} from '../controllers/adminSettingsController';
import {
  validateInput,
  adminRateLimit,
  userAdminRateLimit,
} from '../middleware/security';
import { authenticate, requireAdmin } from '../middleware/auth';
import { body, query, param } from 'express-validator';
import {
  getCachedCurrencyValidation,
  getCachedAmountValidation,
  getCachedVoucherQuantityValidation,
  getCachedExpiryDaysValidation,
  dynamicValidationMiddleware,
} from '../middleware/dynamicValidation';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(requireAdmin);
router.use(adminRateLimit); // IP-based rate limiting
router.use(userAdminRateLimit); // Per-user rate limiting

// User update validation
const updateUserValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid user ID format'),
  body('role')
    .optional()
    .isIn(['user', 'admin'])
    .withMessage('Role must be user or admin'),
  body('emailVerified')
    .optional()
    .isBoolean()
    .withMessage('Email verified must be boolean'),
  body('locked')
    .optional()
    .isBoolean()
    .withMessage('Locked must be boolean'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// User filtering validation
const userFilterValidation = [
  query('search')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Search term too long'),
  query('role')
    .optional()
    .isIn(['user', 'admin'])
    .withMessage('Invalid role filter'),
  query('verified')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Verified filter must be true or false'),
];

// Order filtering validation
const orderFilterValidation = [
  query('status')
    .optional()
    .isIn(['pending', 'payment_pending', 'paid', 'completed', 'cancelled', 'failed'])
    .withMessage('Invalid order status'),
  query('userId')
    .optional()
    .isUUID()
    .withMessage('Invalid user ID format'),
];

// Voucher filtering validation
const voucherFilterValidation = [
  query('status')
    .optional()
    .isIn(['active', 'redeemed', 'expired', 'cancelled'])
    .withMessage('Invalid voucher status'),
  query('userId')
    .optional()
    .isUUID()
    .withMessage('Invalid user ID format'),
];

// Bulk voucher creation validation
const bulkVoucherValidation = [
  body('count')
    .isInt({ min: 1, max: 1000 })
    .withMessage('Count must be between 1 and 1000'),
  body('expiryDays')
    .optional()
    .isInt({ min: 1, max: 3650 })
    .withMessage('Expiry days must be between 1 and 3650'),
];

// Individual voucher creation validation (static parts)
const createVoucherValidationStatic = [
  body('productId')
    .notEmpty()
    .withMessage('Product ID is required'),
  body('name')
    .isLength({ min: 1, max: 255 })
    .withMessage('Name must be between 1 and 255 characters'),
  body('description')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Description must be between 1 and 1000 characters'),
  body('available')
    .optional()
    .isBoolean()
    .withMessage('Available must be a boolean'),
];

// Dynamic voucher creation validation middleware
const createVoucherValidationDynamic = dynamicValidationMiddleware([
  () => getCachedAmountValidation('amount'),
  getCachedCurrencyValidation,
  getCachedExpiryDaysValidation,
  getCachedVoucherQuantityValidation,
]);

// Voucher status update validation
const updateVoucherStatusValidation = [
  param('voucherId')
    .isUUID()
    .withMessage('Invalid voucher ID format'),
  body('status')
    .isIn(['active', 'cancelled'])
    .withMessage('Status must be either active or cancelled'),
];

// Product filtering validation
const productFilterValidation = [
  query('category')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Invalid category'),
  query('available')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Available must be true or false'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Search term must be between 1 and 255 characters'),
];

// Product creation validation (static parts)
const createProductValidationStatic = [
  body('id')
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-z0-9_-]+$/)
    .withMessage('Product ID must be lowercase alphanumeric with underscores/hyphens'),
  body('name')
    .isLength({ min: 1, max: 255 })
    .withMessage('Name must be between 1 and 255 characters'),
  body('description')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Description must be between 1 and 1000 characters'),
  body('category')
    .isLength({ min: 1, max: 50 })
    .withMessage('Category is required'),
  body('imageUrl')
    .optional()
    .isURL()
    .withMessage('Image URL must be valid'),
  body('popular')
    .optional()
    .isBoolean()
    .withMessage('Popular must be a boolean'),
  body('available')
    .optional()
    .isBoolean()
    .withMessage('Available must be a boolean'),
  body('features')
    .optional()
    .isArray()
    .withMessage('Features must be an array'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
];

// Dynamic product creation validation middleware
const createProductValidationDynamic = dynamicValidationMiddleware([
  () => getCachedAmountValidation('minAmount'),
  () => getCachedAmountValidation('maxAmount'),
  getCachedCurrencyValidation,
]);

// Product update validation (static parts - all fields optional)
const updateProductValidationStatic = [
  param('productId')
    .isLength({ min: 1, max: 50 })
    .withMessage('Invalid product ID'),
  body('name')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Name must be between 1 and 255 characters'),
  body('description')
    .optional()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Description must be between 1 and 1000 characters'),
  body('category')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be valid'),
  body('imageUrl')
    .optional()
    .isURL()
    .withMessage('Image URL must be valid'),
  body('popular')
    .optional()
    .isBoolean()
    .withMessage('Popular must be a boolean'),
  body('available')
    .optional()
    .isBoolean()
    .withMessage('Available must be a boolean'),
  body('features')
    .optional()
    .isArray()
    .withMessage('Features must be an array'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
];

// Dynamic product update validation middleware (for optional fields)
const updateProductValidationDynamic = dynamicValidationMiddleware([
  () => getCachedAmountValidation('minAmount'),
  () => getCachedAmountValidation('maxAmount'),
  getCachedCurrencyValidation,
]);

// Category creation validation
const createCategoryValidation = [
  body('id')
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-z0-9_-]+$/)
    .withMessage('Category ID must be lowercase alphanumeric with underscores/hyphens'),
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Name must be between 1 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('icon')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Icon must be less than 100 characters'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  body('active')
    .optional()
    .isBoolean()
    .withMessage('Active must be a boolean'),
];

/**
 * @route   GET /api/v1/admin/stats
 * @desc    Get admin dashboard statistics
 * @access  Private (Admin)
 */
router.get('/stats', getAdminStats);

/**
 * @route   GET /api/v1/admin/users
 * @desc    Get all users with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/users',
  validateInput([...paginationValidation, ...userFilterValidation]),
  getUsers
);

/**
 * @route   PUT /api/v1/admin/users/:id
 * @desc    Update user role or status
 * @access  Private (Admin)
 */
router.put(
  '/users/:id',
  validateInput(updateUserValidation),
  updateUser
);

/**
 * @route   GET /api/v1/admin/orders
 * @desc    Get all orders with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/orders',
  validateInput([...paginationValidation, ...orderFilterValidation]),
  getOrders
);

/**
 * @route   GET /api/v1/admin/vouchers
 * @desc    Get all vouchers with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/vouchers',
  validateInput([...paginationValidation, ...voucherFilterValidation]),
  getVouchers
);

/**
 * @route   POST /api/v1/admin/vouchers/bulk
 * @desc    Bulk create vouchers
 * @access  Private (Admin)
 */
router.post(
  '/vouchers/bulk',
  validateInput(bulkVoucherValidation),
  bulkCreateVouchers
);

/**
 * @route   POST /api/v1/admin/vouchers
 * @desc    Create individual voucher
 * @access  Private (Admin)
 */
router.post(
  '/vouchers',
  validateInput(createVoucherValidationStatic),
  createVoucherValidationDynamic,
  createVoucher
);

/**
 * @route   PATCH /api/v1/admin/vouchers/:voucherId/status
 * @desc    Update voucher status (enable/disable)
 * @access  Private (Admin)
 */
router.patch(
  '/vouchers/:voucherId/status',
  validateInput(updateVoucherStatusValidation),
  updateVoucherStatus
);

// PRODUCT MANAGEMENT ROUTES

/**
 * @route   GET /api/v1/admin/products
 * @desc    Get all products with pagination and filtering
 * @access  Private (Admin)
 */
router.get(
  '/products',
  validateInput([...paginationValidation, ...productFilterValidation]),
  getProducts
);

/**
 * @route   GET /api/v1/admin/products/:productId
 * @desc    Get product by ID
 * @access  Private (Admin)
 */
router.get(
  '/products/:productId',
  getProductById
);

/**
 * @route   POST /api/v1/admin/products
 * @desc    Create new product
 * @access  Private (Admin)
 */
router.post(
  '/products',
  validateInput(createProductValidationStatic),
  createProductValidationDynamic,
  createProduct
);

/**
 * @route   PUT /api/v1/admin/products/:productId
 * @desc    Update product
 * @access  Private (Admin)
 */
router.put(
  '/products/:productId',
  validateInput(updateProductValidationStatic),
  updateProductValidationDynamic,
  updateProduct
);

/**
 * @route   DELETE /api/v1/admin/products/:productId
 * @desc    Delete product
 * @access  Private (Admin)
 */
router.delete(
  '/products/:productId',
  deleteProduct
);

/**
 * @route   GET /api/v1/admin/categories
 * @desc    Get all product categories
 * @access  Private (Admin)
 */
router.get(
  '/categories',
  getProductCategories
);

/**
 * @route   POST /api/v1/admin/categories
 * @desc    Create new product category
 * @access  Private (Admin)
 */
router.post(
  '/categories',
  validateInput(createCategoryValidation),
  createProductCategory
);

// SYSTEM SETTINGS ROUTES

/**
 * @route   GET /api/v1/admin/settings
 * @desc    Get all system settings grouped by category
 * @access  Private (Admin)
 */
router.get('/settings', getSystemSettings);

/**
 * @route   PUT /api/v1/admin/settings
 * @desc    Update system settings
 * @access  Private (Admin)
 */
router.put(
  '/settings',
  validateInput([
    body('settings')
      .isObject()
      .withMessage('Settings object is required'),
    body('changeReason')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Change reason must be less than 500 characters'),
  ]),
  updateSystemSettings
);

/**
 * @route   GET /api/v1/admin/settings/history
 * @desc    Get system settings change history
 * @access  Private (Admin)
 */
router.get(
  '/settings/history',
  validateInput([
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('category')
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage('Category must be valid'),
    query('settingKey')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('Setting key must be valid'),
  ]),
  getSettingsHistory
);

/**
 * @route   POST /api/v1/admin/settings/reset
 * @desc    Reset settings to default values
 * @access  Private (Admin)
 */
router.post(
  '/settings/reset',
  validateInput([
    body('category')
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage('Category must be valid'),
    body('confirmReset')
      .isBoolean()
      .withMessage('Reset confirmation is required'),
  ]),
  resetSettingsToDefault
);

export default router;
