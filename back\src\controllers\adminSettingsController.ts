import { Request, Response } from 'express';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logAdminAction } from '../config/logger';
import { clearSettingsCache } from '../services/settingsService';

// Get all system settings grouped by category
export const getSystemSettings = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;

    const settingsResult = await executeQuery(
      `SELECT 
        category, setting_key, setting_value, data_type, description, 
        is_sensitive, is_readonly, validation_rules, updated_at, updated_by
       FROM system_settings 
       ORDER BY category, setting_key`
    );

    // Group settings by category
    const settingsByCategory: Record<string, any[]> = {};
    
    for (const setting of settingsResult.rows) {
      if (!settingsByCategory[setting.category]) {
        settingsByCategory[setting.category] = [];
      }

      // Mask sensitive values
      const settingValue = setting.is_sensitive 
        ? '***HIDDEN***' 
        : setting.setting_value;

      settingsByCategory[setting.category].push({
        key: setting.setting_key,
        value: settingValue,
        dataType: setting.data_type,
        description: setting.description,
        isSensitive: setting.is_sensitive,
        isReadonly: setting.is_readonly,
        validationRules: setting.validation_rules,
        updatedAt: setting.updated_at,
        updatedBy: setting.updated_by,
      });
    }

    logAdminAction('VIEW_SYSTEM_SETTINGS', adminId, 'settings', 'list', {}, req);

    res.json({
      success: true,
      data: { settings: settingsByCategory },
    });
  } catch (error) {
    logger.error('Get system settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system settings',
    });
  }
};

// Update system settings
export const updateSystemSettings = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { settings, changeReason } = req.body;

    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Settings object is required',
      });
    }

    const queries = [];
    const logEntries = [];
    const updatedSettings = [];

    // Process each category and setting
    for (const [category, categorySettings] of Object.entries(settings)) {
      if (typeof categorySettings !== 'object') continue;

      for (const [settingKey, newValue] of Object.entries(categorySettings as Record<string, any>)) {
        // Get current setting
        const currentSettingResult = await executeQuery(
          'SELECT id, setting_value, data_type, is_readonly, validation_rules FROM system_settings WHERE category = $1 AND setting_key = $2',
          [category, settingKey]
        );

        if (currentSettingResult.rows.length === 0) {
          continue; // Skip non-existent settings
        }

        const currentSetting = currentSettingResult.rows[0];

        // Check if setting is readonly
        if (currentSetting.is_readonly) {
          continue; // Skip readonly settings
        }

        const oldValue = currentSetting.setting_value;
        const stringValue = String(newValue);

        // Only update if value has changed
        if (oldValue !== stringValue) {
          // Validate the new value based on data type
          if (!validateSettingValue(stringValue, currentSetting.data_type, currentSetting.validation_rules)) {
            return res.status(400).json({
              success: false,
              error: `Invalid value for ${category}.${settingKey}`,
            });
          }

          // Add update query
          queries.push({
            text: 'UPDATE system_settings SET setting_value = $1, updated_by = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
            params: [stringValue, adminId, currentSetting.id],
          });

          // Add log entry
          logEntries.push({
            text: `INSERT INTO system_settings_log (setting_id, category, setting_key, old_value, new_value, changed_by, change_reason, ip_address, user_agent)
                   VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
            params: [
              currentSetting.id,
              category,
              settingKey,
              oldValue,
              stringValue,
              adminId,
              changeReason || 'Admin update',
              req.ip,
              req.get('User-Agent') || null,
            ],
          });

          updatedSettings.push({
            category,
            key: settingKey,
            oldValue,
            newValue: stringValue,
          });
        }
      }
    }

    if (queries.length === 0) {
      return res.json({
        success: true,
        message: 'No settings were changed',
        data: { updatedCount: 0 },
      });
    }

    // Execute all updates and log entries in a transaction
    await executeTransaction([...queries, ...logEntries]);

    // Clear settings cache to ensure fresh values are loaded
    clearSettingsCache();

    logAdminAction('UPDATE_SYSTEM_SETTINGS', adminId, 'settings', 'update', {
      updatedSettings,
      changeReason,
    }, req);

    res.json({
      success: true,
      message: `${updatedSettings.length} setting(s) updated successfully`,
      data: { 
        updatedCount: updatedSettings.length,
        updatedSettings,
      },
    });
  } catch (error) {
    logger.error('Update system settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update system settings',
    });
  }
};

// Get system settings change history
export const getSettingsHistory = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { page = 1, limit = 50, category, settingKey } = req.query;

    const offset = (Number(page) - 1) * Number(limit);

    let whereClause = '';
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (category) {
      whereClause += ` WHERE ssl.category = $${paramIndex}`;
      queryParams.push(category);
      paramIndex++;
    }

    if (settingKey) {
      whereClause += whereClause ? ' AND' : ' WHERE';
      whereClause += ` ssl.setting_key = $${paramIndex}`;
      queryParams.push(settingKey);
      paramIndex++;
    }

    // Add limit and offset at the end
    const limitParam = `$${paramIndex}`;
    const offsetParam = `$${paramIndex + 1}`;
    const allParams = [...queryParams, Number(limit), offset];

    const historyResult = await executeQuery(
      `SELECT
        ssl.id, ssl.category, ssl.setting_key, ssl.old_value, ssl.new_value,
        ssl.change_reason, ssl.ip_address, ssl.created_at,
        u.email as changed_by_email, u.telegram_id as changed_by_telegram
       FROM system_settings_log ssl
       JOIN users u ON ssl.changed_by = u.id
       ${whereClause}
       ORDER BY ssl.created_at DESC
       LIMIT ${limitParam} OFFSET ${offsetParam}`,
      allParams
    );

    // Get total count for pagination
    const countResult = await executeQuery(
      `SELECT COUNT(*) as total FROM system_settings_log ssl ${whereClause}`,
      queryParams // Only the WHERE clause params, no limit/offset
    );

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / Number(limit));

    logAdminAction('VIEW_SETTINGS_HISTORY', adminId, 'settings', 'history', {
      category,
      settingKey,
      page,
      limit,
    }, req);

    res.json({
      success: true,
      data: {
        history: historyResult.rows,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages,
        },
      },
    });
  } catch (error) {
    logger.error('Get settings history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get settings history',
    });
  }
};

// Reset settings to default values
export const resetSettingsToDefault = async (req: Request, res: Response) => {
  try {
    const adminId = req.user!.id;
    const { category, confirmReset } = req.body;

    if (!confirmReset) {
      return res.status(400).json({
        success: false,
        error: 'Reset confirmation is required',
      });
    }

    let whereClause = '';
    const params: any[] = [];

    if (category) {
      whereClause = 'WHERE category = $1';
      params.push(category);
    }

    // This would require a default_value column in the schema
    // For now, we'll return an error suggesting manual reset
    return res.status(400).json({
      success: false,
      error: 'Settings reset functionality requires default values to be defined in the database schema',
    });

  } catch (error) {
    logger.error('Reset settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset settings',
    });
  }
};

// Validate setting value based on data type and validation rules
function validateSettingValue(value: string, dataType: string, validationRules: any): boolean {
  try {
    switch (dataType) {
      case 'boolean':
        return ['true', 'false'].includes(value.toLowerCase());
      
      case 'number':
        const num = parseFloat(value);
        if (isNaN(num)) return false;
        
        if (validationRules?.min !== undefined && num < validationRules.min) return false;
        if (validationRules?.max !== undefined && num > validationRules.max) return false;
        return true;
      
      case 'json':
        try {
          JSON.parse(value);
          return true;
        } catch {
          return false;
        }
      
      case 'string':
      default:
        if (validationRules?.minLength && value.length < validationRules.minLength) return false;
        if (validationRules?.maxLength && value.length > validationRules.maxLength) return false;
        if (validationRules?.pattern && !new RegExp(validationRules.pattern).test(value)) return false;
        return true;
    }
  } catch (error) {
    logger.error('Setting validation error:', error);
    return false;
  }
}
