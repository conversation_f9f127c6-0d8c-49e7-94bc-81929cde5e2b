'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { adminApi } from '@/lib/api';
import { toast } from 'react-hot-toast';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  Shield, 
  Users, 
  CreditCard, 
  Mail, 
  Database, 
  Zap,
  AlertTriangle,
  Clock,
  Eye,
  EyeOff,
  History,
  Info
} from 'lucide-react';
import SettingsHistoryModal from '@/components/admin/SettingsHistoryModal';

interface SystemSetting {
  key: string;
  value: string;
  dataType: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  isSensitive: boolean;
  isReadonly: boolean;
  validationRules: any;
  updatedAt: string;
  updatedBy: string;
}

interface SettingsCategory {
  [key: string]: SystemSetting;
}

interface AllSettings {
  [category: string]: SettingsCategory;
}

const categoryIcons: Record<string, any> = {
  security: Shield,
  rate_limiting: Zap,
  user_management: Users,
  payment: CreditCard,
  voucher: CreditCard,
  email: Mail,
  notifications: Mail,
  system: Settings,
  blockchain: Database,
  database: Database,
  performance: Zap,
};

const categoryNames: Record<string, string> = {
  security: 'Security Settings',
  rate_limiting: 'Rate Limiting',
  user_management: 'User Management',
  payment: 'Payment Settings',
  voucher: 'Voucher Settings',
  email: 'Email Configuration',
  notifications: 'Notifications',
  system: 'System Configuration',
  blockchain: 'Blockchain Settings',
  database: 'Database Settings',
  performance: 'Performance Settings',
};

export default function AdminSettingsPage() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<AllSettings>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [modifiedSettings, setModifiedSettings] = useState<AllSettings>({});
  const [changeReason, setChangeReason] = useState('');
  const [showSensitive, setShowSensitive] = useState<Record<string, boolean>>({});
  const [showHistory, setShowHistory] = useState(false);

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchSettings();
    }
  }, [user]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getSystemSettings();
      if (response.success) {
        setSettings(response.data.settings);
        setModifiedSettings(response.data.settings);
        
        // Set first category as active
        const categories = Object.keys(response.data.settings);
        if (categories.length > 0 && !activeCategory) {
          setActiveCategory(categories[0]);
        }
      } else {
        toast.error(response.error || 'Failed to fetch settings');
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
      toast.error('Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (category: string, key: string, value: string) => {
    setModifiedSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: {
          ...prev[category][key],
          value: value,
        },
      },
    }));
  };

  const hasChanges = () => {
    return JSON.stringify(settings) !== JSON.stringify(modifiedSettings);
  };

  const getChangedSettings = () => {
    const changes: Record<string, Record<string, string>> = {};
    
    Object.keys(modifiedSettings).forEach(category => {
      Object.keys(modifiedSettings[category]).forEach(key => {
        const original = settings[category]?.[key]?.value;
        const modified = modifiedSettings[category][key].value;
        
        if (original !== modified) {
          if (!changes[category]) {
            changes[category] = {};
          }
          changes[category][key] = modified;
        }
      });
    });
    
    return changes;
  };

  const handleSave = async () => {
    if (!hasChanges()) {
      toast.error('No changes to save');
      return;
    }

    if (!changeReason.trim()) {
      toast.error('Please provide a reason for the changes');
      return;
    }

    setSaving(true);
    try {
      const changedSettings = getChangedSettings();
      
      const response = await adminApi.updateSystemSettings({
        settings: changedSettings,
        changeReason: changeReason.trim(),
      });

      if (response.success) {
        toast.success(response.message || 'Settings updated successfully');
        setSettings(modifiedSettings);
        setChangeReason('');
        fetchSettings(); // Refresh to get updated timestamps
      } else {
        toast.error(response.error || 'Failed to update settings');
      }
    } catch (error: any) {
      console.error('Failed to update settings:', error);
      toast.error(error.response?.data?.error || 'Failed to update settings');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (hasChanges()) {
      if (confirm('Are you sure you want to discard all changes?')) {
        setModifiedSettings(settings);
        setChangeReason('');
        toast.success('Changes discarded');
      }
    }
  };

  const toggleSensitiveVisibility = (category: string, key: string) => {
    const settingKey = `${category}.${key}`;
    setShowSensitive(prev => ({
      ...prev,
      [settingKey]: !prev[settingKey],
    }));
  };

  const renderSettingInput = (category: string, setting: SystemSetting) => {
    const settingKey = `${category}.${setting.key}`;
    const isVisible = showSensitive[settingKey];
    const currentValue = modifiedSettings[category]?.[setting.key]?.value || setting.value;

    if (setting.isReadonly) {
      return (
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={currentValue}
            disabled
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed"
          />
          <Info className="h-4 w-4 text-gray-400" title="This setting is read-only" />
        </div>
      );
    }

    switch (setting.dataType) {
      case 'boolean':
        return (
          <select
            value={currentValue}
            onChange={(e) => handleSettingChange(category, setting.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        );

      case 'number':
        return (
          <input
            type="number"
            value={currentValue}
            onChange={(e) => handleSettingChange(category, setting.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            min={setting.validationRules?.min}
            max={setting.validationRules?.max}
          />
        );

      case 'json':
        return (
          <textarea
            value={currentValue}
            onChange={(e) => handleSettingChange(category, setting.key, e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            placeholder="Valid JSON format"
          />
        );

      default: // string
        if (setting.isSensitive) {
          return (
            <div className="flex items-center space-x-2">
              <input
                type={isVisible ? 'text' : 'password'}
                value={isVisible ? currentValue : '***HIDDEN***'}
                onChange={(e) => handleSettingChange(category, setting.key, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled={!isVisible}
              />
              <button
                type="button"
                onClick={() => toggleSensitiveVisibility(category, setting.key)}
                className="p-2 text-gray-500 hover:text-gray-700"
                title={isVisible ? 'Hide value' : 'Show value'}
              >
                {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          );
        }

        return (
          <input
            type="text"
            value={currentValue}
            onChange={(e) => handleSettingChange(category, setting.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            minLength={setting.validationRules?.minLength}
            maxLength={setting.validationRules?.maxLength}
          />
        );
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const categories = Object.keys(settings);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure system-wide settings and preferences
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowHistory(!showHistory)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <History className="h-4 w-4 mr-2" />
            Change History
          </button>
        </div>
      </div>

      {/* Warning Banner */}
      {hasChanges() && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Unsaved Changes
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                You have unsaved changes. Make sure to save them before leaving this page.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white shadow rounded-lg">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Categories</h3>
            </div>
            <nav className="space-y-1 p-2">
              {categories.map((category) => {
                const Icon = categoryIcons[category] || Settings;
                const isActive = activeCategory === category;
                
                return (
                  <button
                    key={category}
                    onClick={() => setActiveCategory(category)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-3" />
                    {categoryNames[category] || category}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {activeCategory && settings[activeCategory] && (
            <div className="bg-white shadow rounded-lg">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {categoryNames[activeCategory] || activeCategory}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Configure {(categoryNames[activeCategory] || activeCategory).toLowerCase()} options
                </p>
              </div>
              
              <div className="p-6 space-y-6">
                {Object.entries(settings[activeCategory]).map(([key, setting]) => (
                  <div key={key} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <label className="block text-sm font-medium text-gray-700">
                        {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        {setting.isReadonly && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            Read-only
                          </span>
                        )}
                        {setting.isSensitive && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            Sensitive
                          </span>
                        )}
                      </label>
                      <span className="text-xs text-gray-500">
                        {setting.dataType}
                      </span>
                    </div>
                    
                    {renderSettingInput(activeCategory, setting)}
                    
                    {setting.description && (
                      <p className="text-sm text-gray-500">{setting.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Save Actions */}
      {hasChanges() && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Change Reason *
              </label>
              <textarea
                value={changeReason}
                onChange={(e) => setChangeReason(e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe the reason for these changes..."
                required
              />
            </div>
            
            <div className="flex items-center justify-end space-x-3">
              <button
                onClick={handleReset}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Discard Changes
              </button>
              <button
                onClick={handleSave}
                disabled={saving || !changeReason.trim()}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Settings History Modal */}
      <SettingsHistoryModal
        isOpen={showHistory}
        onClose={() => setShowHistory(false)}
      />
    </div>
  );
}
