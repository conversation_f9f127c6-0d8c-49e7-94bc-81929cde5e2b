import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';
import fs from 'fs';
import path from 'path';

// Direct database configuration
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

const setupTestData = async () => {
  const db = new Pool(dbConfig);
  
  try {
    console.log('🚀 Setting up test data...');

    // Test database connection
    await db.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Run migration for products table
    console.log('📦 Running products migration...');
    const migrationPath = path.join(process.cwd(), 'database', 'migrations', '002_add_products_table.sql');
    
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      // Split SQL by semicolons and execute each statement separately
      const statements = migrationSQL.split(';').filter(stmt => stmt.trim().length > 0);
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await db.query(statement.trim());
          } catch (error: any) {
            // Ignore "already exists" errors
            if (!error.message.includes('already exists') && !error.message.includes('duplicate key')) {
              console.error('Migration error:', error.message);
            }
          }
        }
      }
      console.log('✅ Products migration completed');
    } else {
      console.log(`⚠️  Products migration file not found at: ${migrationPath}`);
    }

    // Create test user if not exists
    console.log('👤 Creating test user...');
    const testEmail = '<EMAIL>';
    const testTelegramId = 'test_user_1233';
    const testPassword = 'TestPassword123!';
    
    const existingUser = await db.query(
      'SELECT id FROM users WHERE email = $1',
      [testEmail]
    );

    let testUserId: string;
    
    if (existingUser.rows.length === 0) {
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      const userResult = await db.query(
        `INSERT INTO users (id, email, telegram_id, password_hash, email_verified, role)
         VALUES ($1, $2, $3, $4, true, 'user')
         RETURNING id`,
        [uuidv4(), testEmail, testTelegramId, hashedPassword]
      );
      testUserId = userResult.rows[0].id;
      console.log(`✅ Test user created with ID: ${testUserId}`);
    } else {
      testUserId = existingUser.rows[0].id;
      console.log(`✅ Test user already exists with ID: ${testUserId}`);
    }

    // Create test orders
    console.log('📋 Creating test orders...');
    
    const testOrders = [
      {
        id: uuidv4(),
        productId: 'steam',
        amount: 25.00,
        status: 'completed',
        memo: 'Steam gift card for gaming',
        quantity: 1,
      },
      {
        id: uuidv4(),
        productId: 'netflix',
        amount: 15.00,
        status: 'completed',
        memo: 'Netflix subscription gift',
        quantity: 1,
      },
      {
        id: uuidv4(),
        productId: 'amazon',
        amount: 50.00,
        status: 'pending',
        memo: 'Amazon shopping voucher',
        quantity: 1,
      },
      {
        id: uuidv4(),
        productId: 'spotify',
        amount: 10.00,
        status: 'payment_pending',
        memo: 'Spotify Premium subscription',
        quantity: 1,
      },
    ];

    for (const order of testOrders) {
      // Check if order already exists
      const existingOrder = await db.query(
        'SELECT id FROM orders WHERE id = $1',
        [order.id]
      );

      if (existingOrder.rows.length === 0) {
        await db.query(
          `INSERT INTO orders (id, user_id, status, amount, currency, memo, product_id, quantity, payment_address, created_at, updated_at)
           VALUES ($1, $2, $3, $4, 'TON', $5, $6, $7, $8, NOW() - INTERVAL '30 days', NOW())`,
          [
            order.id,
            testUserId,
            order.status,
            order.amount,
            order.memo,
            order.productId,
            order.quantity,
            `EQD${Math.random().toString(36).substring(2, 15)}`, // Mock TON address
          ]
        );
        console.log(`✅ Created test order: ${order.productId} - ${order.status}`);
      } else {
        console.log(`⚠️  Order already exists: ${order.productId}`);
      }
    }

    // Create test vouchers for completed orders
    console.log('🎫 Creating test vouchers...');
    
    const completedOrders = await db.query(
      'SELECT id, product_id, amount FROM orders WHERE user_id = $1 AND status = $2',
      [testUserId, 'completed']
    );

    for (const order of completedOrders.rows) {
      // Check if voucher already exists
      const existingVoucher = await db.query(
        'SELECT id FROM vouchers WHERE order_id = $1',
        [order.id]
      );

      if (existingVoucher.rows.length === 0) {
        const voucherCode = `${order.product_id.toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
        const expiresAt = new Date();
        expiresAt.setFullYear(expiresAt.getFullYear() + 1); // 1 year from now

        await db.query(
          `INSERT INTO vouchers (id, order_id, code, status, expires_at, created_at)
           VALUES ($1, $2, $3, 'active', $4, NOW() - INTERVAL '30 days')`,
          [uuidv4(), order.id, voucherCode, expiresAt]
        );
        console.log(`✅ Created test voucher: ${voucherCode}`);
      } else {
        console.log(`⚠️  Voucher already exists for order: ${order.id}`);
      }
    }

    // Create some redeemed vouchers for testing
    console.log('🎫 Creating redeemed vouchers...');
    
    const additionalOrders = [
      {
        id: uuidv4(),
        productId: 'playstation',
        amount: 30.00,
        status: 'completed',
        memo: 'PlayStation Store gift card',
        quantity: 1,
      },
    ];

    for (const order of additionalOrders) {
      const existingOrder = await db.query(
        'SELECT id FROM orders WHERE id = $1',
        [order.id]
      );

      if (existingOrder.rows.length === 0) {
        await db.query(
          `INSERT INTO orders (id, user_id, status, amount, currency, memo, product_id, quantity, payment_address, created_at, updated_at)
           VALUES ($1, $2, $3, $4, 'TON', $5, $6, $7, $8, NOW() - INTERVAL '45 days', NOW())`,
          [
            order.id,
            testUserId,
            order.status,
            order.amount,
            order.memo,
            order.productId,
            order.quantity,
            `EQD${Math.random().toString(36).substring(2, 15)}`,
          ]
        );

        // Create redeemed voucher
        const voucherCode = `${order.productId.toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
        const redeemedAt = new Date();
        redeemedAt.setDate(redeemedAt.getDate() - 10); // Redeemed 10 days ago

        await db.query(
          `INSERT INTO vouchers (id, order_id, code, status, redeemed_at, redeemed_by_user_id, expires_at, created_at)
           VALUES ($1, $2, $3, 'redeemed', $4, $5, $6, NOW() - INTERVAL '45 days')`,
          [
            uuidv4(),
            order.id,
            voucherCode,
            redeemedAt,
            testUserId,
            new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          ]
        );
        console.log(`✅ Created redeemed voucher: ${voucherCode}`);
      }
    }

    console.log('🎉 Test data setup completed successfully!');
    console.log(`📧 Test user credentials: ${testEmail} / ${testPassword}`);
    
  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  } finally {
    await db.end();
  }
};

// Run the setup
setupTestData()
  .then(() => {
    console.log('✅ Test data setup finished');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test data setup failed:', error);
    process.exit(1);
  });
