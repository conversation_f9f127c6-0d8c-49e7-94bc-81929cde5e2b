import { Request, Response } from 'express';
import { executeQuery } from '../config/database';
import { logger } from '../config/logger';
import { sanitizeString } from '../utils/helpers';
import bcrypt from 'bcrypt';
import { settingsService } from '../services/settingsService';

// Get user profile
export const getUserProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const userResult = await executeQuery(
      `SELECT u.id, u.email, u.telegram_id, u.email_verified, u.role, u.created_at, u.last_login,
              COUNT(DISTINCT o.id) as total_orders,
              COUNT(DISTINCT v.id) as total_vouchers,
              COALESCE(SUM(CASE WHEN o.status = 'completed' THEN o.amount ELSE 0 END), 0) as total_spent
       FROM users u
       LEFT JOIN orders o ON u.id = o.user_id
       LEFT JOIN vouchers v ON o.id = v.order_id
       WHERE u.id = $1
       GROUP BY u.id, u.email, u.telegram_id, u.email_verified, u.role, u.created_at, u.last_login`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    // Get recent activity
    const recentOrdersResult = await executeQuery(
      `SELECT o.id, o.status, o.amount, o.currency, o.created_at, p.name as product_name
       FROM orders o
       LEFT JOIN products p ON o.product_id = p.id
       WHERE o.user_id = $1
       ORDER BY o.created_at DESC
       LIMIT 5`,
      [userId]
    );

    const recentVouchersResult = await executeQuery(
      `SELECT v.id, v.code, v.status, v.created_at, v.expires_at, p.name as product_name
       FROM vouchers v
       JOIN orders o ON v.order_id = o.id
       LEFT JOIN products p ON o.product_id = p.id
       WHERE o.user_id = $1
       ORDER BY v.created_at DESC
       LIMIT 5`,
      [userId]
    );

    res.json({
      success: true,
      data: {
        profile: {
          id: user.id,
          email: user.email,
          telegramId: user.telegram_id,
          emailVerified: user.email_verified,
          role: user.role,
          createdAt: user.created_at,
          lastLogin: user.last_login,
          stats: {
            totalOrders: parseInt(user.total_orders),
            totalVouchers: parseInt(user.total_vouchers),
            totalSpent: parseFloat(user.total_spent),
          },
        },
        recentActivity: {
          orders: recentOrdersResult.rows.map((row: any) => ({
            id: row.id,
            status: row.status,
            amount: parseFloat(row.amount),
            currency: row.currency,
            productName: row.product_name,
            createdAt: row.created_at,
          })),
          vouchers: recentVouchersResult.rows.map((row: any) => ({
            id: row.id,
            code: row.code,
            status: row.status,
            productName: row.product_name,
            createdAt: row.created_at,
            expiresAt: row.expires_at,
          })),
        },
      },
    });
  } catch (error) {
    logger.error('Get user profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile',
    });
  }
};

// Update user profile
export const updateUserProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { email, telegramId } = req.body;

    // Validate input
    if (!email || !telegramId) {
      return res.status(400).json({
        success: false,
        error: 'Email and Telegram ID are required',
      });
    }

    // Sanitize input
    const sanitizedEmail = sanitizeString(email.toLowerCase().trim());
    const sanitizedTelegramId = sanitizeString(telegramId.trim());

    // Check if email is already taken by another user
    if (sanitizedEmail !== req.user!.email) {
      const emailCheck = await executeQuery(
        'SELECT id FROM users WHERE email = $1 AND id != $2',
        [sanitizedEmail, userId]
      );

      if (emailCheck.rows.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Email is already taken',
        });
      }
    }

    // Check if telegram ID is already taken by another user
    if (sanitizedTelegramId !== req.user!.telegramId) {
      const telegramCheck = await executeQuery(
        'SELECT id FROM users WHERE telegram_id = $1 AND id != $2',
        [sanitizedTelegramId, userId]
      );

      if (telegramCheck.rows.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Telegram ID is already taken',
        });
      }
    }

    // Update user profile
    const updateResult = await executeQuery(
      `UPDATE users 
       SET email = $1, telegram_id = $2, updated_at = NOW()
       WHERE id = $3
       RETURNING id, email, telegram_id, email_verified, role, created_at, updated_at`,
      [sanitizedEmail, sanitizedTelegramId, userId]
    );

    if (updateResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const updatedUser = updateResult.rows[0];

    // Log security event if email changed
    if (sanitizedEmail !== req.user!.email) {
      logger.info(`User ${userId} changed email from ${req.user!.email} to ${sanitizedEmail}`);
    }

    res.json({
      success: true,
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          telegramId: updatedUser.telegram_id,
          emailVerified: updatedUser.email_verified,
          role: updatedUser.role,
          createdAt: updatedUser.created_at,
          updatedAt: updatedUser.updated_at,
        },
      },
      message: 'Profile updated successfully',
    });
  } catch (error) {
    logger.error('Update user profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile',
    });
  }
};

// Change password
export const changePassword = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { currentPassword, newPassword } = req.body;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required',
      });
    }

    // Get current password hash
    const userResult = await executeQuery(
      'SELECT password_hash FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      });
    }

    const user = userResult.rows[0];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect',
      });
    }

    // Validate new password strength using dynamic security settings
    try {
      const securitySettings = await settingsService.getSecuritySettings();

      // Check minimum length
      if (newPassword.length < securitySettings.passwordMinLength) {
        return res.status(400).json({
          success: false,
          error: `New password must be at least ${securitySettings.passwordMinLength} characters long`,
        });
      }

      // Check character requirements
      const errors = [];
      if (securitySettings.passwordRequireLowercase && !/(?=.*[a-z])/.test(newPassword)) {
        errors.push('lowercase letter');
      }
      if (securitySettings.passwordRequireUppercase && !/(?=.*[A-Z])/.test(newPassword)) {
        errors.push('uppercase letter');
      }
      if (securitySettings.passwordRequireNumbers && !/(?=.*\d)/.test(newPassword)) {
        errors.push('number');
      }
      if (securitySettings.passwordRequireSymbols && !/(?=.*[@$!%*?&])/.test(newPassword)) {
        errors.push('special character (@$!%*?&)');
      }

      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          error: `Password must contain at least one ${errors.join(', ')}`,
        });
      }
    } catch (error) {
      logger.error('Failed to get security settings for password validation:', error);
      // Fallback to basic validation
      if (newPassword.length < 8) {
        return res.status(400).json({
          success: false,
          error: 'New password must be at least 8 characters long',
        });
      }
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await executeQuery(
      'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
      [newPasswordHash, userId]
    );

    // Log security event
    logger.info(`User ${userId} changed password`);

    res.json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to change password',
    });
  }
};

// Get account summary
export const getAccountSummary = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get comprehensive account statistics
    const summaryResult = await executeQuery(
      `SELECT 
         COUNT(DISTINCT o.id) as total_orders,
         COUNT(DISTINCT CASE WHEN o.status = 'completed' THEN o.id END) as completed_orders,
         COUNT(DISTINCT CASE WHEN o.status = 'pending' THEN o.id END) as pending_orders,
         COUNT(DISTINCT v.id) as total_vouchers,
         COUNT(DISTINCT CASE WHEN v.status = 'active' THEN v.id END) as active_vouchers,
         COUNT(DISTINCT CASE WHEN v.status = 'redeemed' THEN v.id END) as redeemed_vouchers,
         COALESCE(SUM(CASE WHEN o.status = 'completed' THEN o.amount ELSE 0 END), 0) as total_spent,
         COALESCE(AVG(CASE WHEN o.status = 'completed' THEN o.amount END), 0) as avg_order_value
       FROM orders o
       LEFT JOIN vouchers v ON o.id = v.order_id
       WHERE o.user_id = $1`,
      [userId]
    );

    const summary = summaryResult.rows[0];

    // Get monthly spending for the last 6 months
    const monthlySpendingResult = await executeQuery(
      `SELECT 
         DATE_TRUNC('month', o.created_at) as month,
         COUNT(o.id) as order_count,
         COALESCE(SUM(o.amount), 0) as total_amount
       FROM orders o
       WHERE o.user_id = $1 
         AND o.status = 'completed'
         AND o.created_at >= NOW() - INTERVAL '6 months'
       GROUP BY DATE_TRUNC('month', o.created_at)
       ORDER BY month DESC`,
      [userId]
    );

    res.json({
      success: true,
      data: {
        summary: {
          totalOrders: parseInt(summary.total_orders),
          completedOrders: parseInt(summary.completed_orders),
          pendingOrders: parseInt(summary.pending_orders),
          totalVouchers: parseInt(summary.total_vouchers),
          activeVouchers: parseInt(summary.active_vouchers),
          redeemedVouchers: parseInt(summary.redeemed_vouchers),
          totalSpent: parseFloat(summary.total_spent),
          averageOrderValue: parseFloat(summary.avg_order_value),
        },
        monthlySpending: monthlySpendingResult.rows.map((row: any) => ({
          month: row.month,
          orderCount: parseInt(row.order_count),
          totalAmount: parseFloat(row.total_amount),
        })),
      },
    });
  } catch (error) {
    logger.error('Get account summary error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get account summary',
    });
  }
};
