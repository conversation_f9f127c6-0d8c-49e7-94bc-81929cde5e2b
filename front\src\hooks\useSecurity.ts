import { useSettings } from '../contexts/SettingsContext';

/**
 * Custom hook for accessing security settings
 * Provides easy access to security settings and helper functions for password validation
 */
export const useSecurity = () => {
  const { getSecuritySettings } = useSettings();
  const securitySettings = getSecuritySettings();

  // Helper functions to get specific security settings
  const getPasswordMinLength = () => {
    return securitySettings?.passwordMinLength ?? 8;
  };

  const isPasswordRequireLowercase = () => {
    return securitySettings?.passwordRequireLowercase ?? true;
  };

  const isPasswordRequireUppercase = () => {
    return securitySettings?.passwordRequireUppercase ?? true;
  };

  const isPasswordRequireNumbers = () => {
    return securitySettings?.passwordRequireNumbers ?? true;
  };

  const isPasswordRequireSymbols = () => {
    return securitySettings?.passwordRequireSymbols ?? true;
  };

  const isTwoFactorEnabled = () => {
    return securitySettings?.enableTwoFactor ?? false;
  };

  // Password validation function based on current settings
  const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const minLength = getPasswordMinLength();

    if (!password) {
      errors.push('Password is required');
      return { isValid: false, errors };
    }

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }

    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }

    if (isPasswordRequireLowercase() && !/(?=.*[a-z])/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (isPasswordRequireUppercase() && !/(?=.*[A-Z])/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (isPasswordRequireNumbers() && !/(?=.*\d)/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (isPasswordRequireSymbols() && !/(?=.*[@$!%*?&])/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  // Get password requirements as a readable string
  const getPasswordRequirements = (): string[] => {
    const requirements: string[] = [];
    const minLength = getPasswordMinLength();

    requirements.push(`At least ${minLength} characters`);

    if (isPasswordRequireLowercase()) {
      requirements.push('One lowercase letter');
    }

    if (isPasswordRequireUppercase()) {
      requirements.push('One uppercase letter');
    }

    if (isPasswordRequireNumbers()) {
      requirements.push('One number');
    }

    if (isPasswordRequireSymbols()) {
      requirements.push('One special character (@$!%*?&)');
    }

    return requirements;
  };

  // Calculate password strength (0-100)
  const calculatePasswordStrength = (password: string): number => {
    if (!password) return 0;

    let score = 0;
    const minLength = getPasswordMinLength();

    // Length score (40% of total)
    if (password.length >= minLength) {
      score += 20;
    }
    if (password.length >= minLength + 4) {
      score += 20;
    }

    // Character type scores (60% of total)
    if (/(?=.*[a-z])/.test(password)) score += 15;
    if (/(?=.*[A-Z])/.test(password)) score += 15;
    if (/(?=.*\d)/.test(password)) score += 15;
    if (/(?=.*[@$!%*?&])/.test(password)) score += 15;

    return Math.min(score, 100);
  };

  return {
    securitySettings,
    getPasswordMinLength,
    isPasswordRequireLowercase,
    isPasswordRequireUppercase,
    isPasswordRequireNumbers,
    isPasswordRequireSymbols,
    isTwoFactorEnabled,
    validatePassword,
    getPasswordRequirements,
    calculatePasswordStrength,
  };
};
