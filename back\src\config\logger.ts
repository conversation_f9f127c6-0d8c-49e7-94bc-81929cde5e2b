import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment and settings
const level = (): string => {
  // Check for LOG_LEVEL environment variable first
  if (process.env.LOG_LEVEL) {
    return process.env.LOG_LEVEL;
  }

  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define format for file logs (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Define transports based on environment
const createTransports = (): winston.transport[] => {
  const transports: winston.transport[] = [
    // Console transport (always present)
    new winston.transports.Console({
      level: level(),
      format,
    }),
  ];

  // File transports (skip in test environment)
  if (process.env.NODE_ENV !== 'test') {
    // Error log file
    transports.push(
      new winston.transports.File({
        filename: path.join(process.cwd(), 'logs', 'error.log'),
        level: 'error',
        format: fileFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );

    // Combined log file
    transports.push(
      new winston.transports.File({
        filename: path.join(process.cwd(), 'logs', 'combined.log'),
        format: fileFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );

    // Security events log file
    transports.push(
      new winston.transports.File({
        filename: path.join(process.cwd(), 'logs', 'security.log'),
        level: 'warn',
        format: fileFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 10, // Keep more security logs
      })
    );

    // Performance log file (debug level and above)
    if (level() === 'debug') {
      transports.push(
        new winston.transports.File({
          filename: path.join(process.cwd(), 'logs', 'performance.log'),
          level: 'debug',
          format: fileFormat,
          maxsize: 5242880, // 5MB
          maxFiles: 3,
        })
      );
    }
  }

  return transports;
};

const transports = createTransports();

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Security-focused logging functions
export const logSecurityEvent = (event: string, details: any, req?: any) => {
  const logData = {
    event,
    details,
    timestamp: new Date().toISOString(),
    ip: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    userId: req?.user?.id,
  };
  
  logger.warn('SECURITY_EVENT', logData);
};

export const logAuthEvent = (event: string, userId: string, success: boolean, req?: any) => {
  const logData = {
    event,
    userId,
    success,
    timestamp: new Date().toISOString(),
    ip: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
  };
  
  logger.info('AUTH_EVENT', logData);
};

export const logAdminAction = (action: string, adminId: string, resourceType: string, resourceId: string, details: any, req?: any) => {
  const logData = {
    action,
    adminId,
    resourceType,
    resourceId,
    details,
    timestamp: new Date().toISOString(),
    ip: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
  };
  
  logger.info('ADMIN_ACTION', logData);
};

export const logPaymentEvent = (event: string, orderId: string, amount: string, details: any) => {
  const logData = {
    event,
    orderId,
    amount,
    details,
    timestamp: new Date().toISOString(),
  };
  
  logger.info('PAYMENT_EVENT', logData);
};

export const logVoucherEvent = (event: string, voucherCode: string, userId: string, details: any) => {
  const logData = {
    event,
    voucherCode,
    userId,
    details,
    timestamp: new Date().toISOString(),
  };
  
  logger.info('VOUCHER_EVENT', logData);
};

// Enhanced error logging with comprehensive context
export const logError = (error: Error, context?: any) => {
  const errorData = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    context: sanitizeForLogging(context),
    timestamp: new Date().toISOString(),
    errorId: generateErrorId(),
  };

  logger.error('Application Error', errorData);
  return errorData.errorId;
};

// Generate unique error ID for tracking
const generateErrorId = (): string => {
  return `ERR_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Enhanced error logging with request context
export const logErrorWithRequest = (error: Error, req?: any, additionalContext?: any) => {
  const requestContext = req ? {
    requestId: req.id || req.headers['x-request-id'],
    method: req.method,
    url: req.originalUrl || req.url,
    ip: req.ip || req.connection?.remoteAddress,
    userAgent: req.get?.('User-Agent'),
    userId: req.user?.id,
    sessionId: req.sessionID,
    correlationId: req.headers['x-correlation-id'],
  } : {};

  const errorData = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    request: requestContext,
    context: sanitizeForLogging(additionalContext),
    timestamp: new Date().toISOString(),
    errorId: generateErrorId(),
  };

  logger.error('Request Error', errorData);
  return errorData.errorId;
};

// Business operation error logging
export const logBusinessError = (operation: string, error: Error, businessContext?: any, req?: any) => {
  const errorData = {
    operation,
    message: error.message,
    stack: error.stack,
    name: error.name,
    businessContext: sanitizeForLogging(businessContext),
    request: req ? {
      requestId: req.id || req.headers['x-request-id'],
      userId: req.user?.id,
      ip: req.ip,
    } : {},
    timestamp: new Date().toISOString(),
    errorId: generateErrorId(),
  };

  logger.error('Business Operation Error', errorData);
  return errorData.errorId;
};

// Performance logging
export const logPerformance = (operation: string, duration: number, details?: any) => {
  logger.info('PERFORMANCE', {
    operation,
    duration,
    details,
    timestamp: new Date().toISOString(),
  });
};

// Enhanced database operation logging
export const logDatabaseOperation = (operation: string, table: string, duration: number, success: boolean, context?: any) => {
  const logData = {
    operation,
    table,
    duration,
    success,
    context: sanitizeForLogging(context),
    timestamp: new Date().toISOString(),
  };

  if (success) {
    logger.debug('DATABASE_OPERATION', logData);
  } else {
    logger.error('DATABASE_OPERATION_FAILED', logData);
  }
};

// Database error logging with query context
export const logDatabaseError = (error: Error, query: string, params?: any[], context?: any) => {
  const errorData = {
    message: error.message,
    stack: error.stack,
    query: query.substring(0, 500), // Limit query length in logs
    paramCount: params?.length || 0,
    context: sanitizeForLogging(context),
    timestamp: new Date().toISOString(),
    errorId: generateErrorId(),
  };

  logger.error('Database Error', errorData);
  return errorData.errorId;
};

// API request logging
export const logApiRequest = (method: string, url: string, statusCode: number, duration: number, userId?: string) => {
  logger.http('API_REQUEST', {
    method,
    url,
    statusCode,
    duration,
    userId,
    timestamp: new Date().toISOString(),
  });
};

// Sanitize sensitive data for logging
export const sanitizeForLogging = (data: any): any => {
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  const sensitiveFields = [
    'password',
    'token',
    'secret',
    'key',
    'hash',
    'authorization',
    'cookie',
    'session',
    'csrf',
    'mnemonic',
    'privateKey',
    'private_key',
  ];
  
  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '***REDACTED***';
    }
  }
  
  // Recursively sanitize nested objects
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeForLogging(sanitized[key]);
    }
  }
  
  return sanitized;
};

// Service operation logging
export const logServiceOperation = (service: string, operation: string, duration: number, success: boolean, context?: any) => {
  const logData = {
    service,
    operation,
    duration,
    success,
    context: sanitizeForLogging(context),
    timestamp: new Date().toISOString(),
  };

  if (success) {
    logger.info('SERVICE_OPERATION', logData);
  } else {
    logger.error('SERVICE_OPERATION_FAILED', logData);
  }
};

// External API call logging
export const logExternalApiCall = (service: string, endpoint: string, method: string, statusCode: number, duration: number, context?: any) => {
  const logData = {
    service,
    endpoint,
    method,
    statusCode,
    duration,
    success: statusCode >= 200 && statusCode < 300,
    context: sanitizeForLogging(context),
    timestamp: new Date().toISOString(),
  };

  if (logData.success) {
    logger.info('EXTERNAL_API_CALL', logData);
  } else {
    logger.warn('EXTERNAL_API_CALL_FAILED', logData);
  }
};

// Critical error logging (for alerts)
export const logCriticalError = (error: Error, operation: string, context?: any, req?: any) => {
  const errorData = {
    level: 'CRITICAL',
    operation,
    message: error.message,
    stack: error.stack,
    name: error.name,
    context: sanitizeForLogging(context),
    request: req ? {
      requestId: req.id || req.headers['x-request-id'],
      userId: req.user?.id,
      ip: req.ip,
      url: req.originalUrl,
    } : {},
    timestamp: new Date().toISOString(),
    errorId: generateErrorId(),
  };

  logger.error('CRITICAL_ERROR', errorData);

  // In production, this could trigger alerts (email, Slack, etc.)
  if (process.env.NODE_ENV === 'production') {
    // TODO: Integrate with alerting system
    console.error('🚨 CRITICAL ERROR DETECTED:', errorData.errorId);
  }

  return errorData.errorId;
};

// Validation error logging
export const logValidationError = (errors: any[], req?: any, context?: any) => {
  const logData = {
    errors: errors.map(err => ({
      field: err.param || err.path,
      message: err.msg || err.message,
      value: err.value,
    })),
    request: req ? {
      requestId: req.id || req.headers['x-request-id'],
      method: req.method,
      url: req.originalUrl,
      userId: req.user?.id,
      ip: req.ip,
    } : {},
    context: sanitizeForLogging(context),
    timestamp: new Date().toISOString(),
  };

  logger.warn('VALIDATION_ERROR', logData);
};
