import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { body, validationResult, ValidationChain } from 'express-validator';
import { logger, logSecurityEvent } from '../config/logger';
import { redis } from '../config/database';
import { settingsService } from '../services/settingsService';

// Import User type from auth middleware
import { User } from './auth';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// Per-user rate limiting store using Redis
class UserRateLimitStore {
  private windowMs: number;
  private keyPrefix: string;

  constructor(windowMs: number, keyPrefix: string = 'user_rate_limit') {
    this.windowMs = windowMs;
    this.keyPrefix = keyPrefix;
  }

  private getKey(userId: string, identifier: string): string {
    return `${this.keyPrefix}:${identifier}:${userId}`;
  }

  async increment(userId: string, identifier: string): Promise<{ totalHits: number; resetTime: Date }> {
    const key = this.getKey(userId, identifier);
    const now = Date.now();
    const windowStart = now - this.windowMs;

    try {
      // Use Redis multi for atomic operations
      const multi = redis.multi();

      // Remove expired entries
      multi.zRemRangeByScore(key, 0, windowStart);

      // Add current request
      multi.zAdd(key, { score: now, value: `${now}-${Math.random()}` });

      // Count current requests in window
      multi.zCard(key);

      // Set expiration
      multi.expire(key, Math.ceil(this.windowMs / 1000));

      const results = await multi.exec();

      if (!results) {
        throw new Error('Redis multi failed');
      }

      const totalHits = results[2] as number;
      const resetTime = new Date(now + this.windowMs);

      return { totalHits, resetTime };
    } catch (error) {
      logger.error('User rate limit store error:', error);
      // Fallback: allow request but log error
      return { totalHits: 1, resetTime: new Date(now + this.windowMs) };
    }
  }

  async reset(userId: string, identifier: string): Promise<void> {
    const key = this.getKey(userId, identifier);
    try {
      await redis.del(key);
    } catch (error) {
      logger.error('Failed to reset user rate limit:', error);
    }
  }
}

// CORS configuration
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logSecurityEvent('CORS_VIOLATION', { origin, allowedOrigins });
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With'],
  exposedHeaders: ['X-CSRF-Token'],
};

// Helmet security configuration
export const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

// Rate limiting configurations
export const createRateLimit = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: message || 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      logSecurityEvent('RATE_LIMIT_EXCEEDED', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
      }, req);
      
      res.status(429).json({
        success: false,
        error: 'Too many requests, please try again later.',
      });
    },
    skip: (req: Request) => {
      // Skip rate limiting for health checks and admin routes (they have their own rate limiting)
      return req.path === '/health' ||
             req.path === '/api/health' ||
             req.path.startsWith('/api/v1/admin');
    },
  });
};

// Dynamic rate limiting based on performance settings
export const createDynamicRateLimit = async () => {
  try {
    const performanceSettings = await settingsService.getPerformanceSettings();
    const maxRequests = Math.min(performanceSettings.maxConcurrentRequests, 1000); // Cap at 1000 for safety

    return createRateLimit(
      15 * 60 * 1000, // 15 minutes
      maxRequests,
      'Too many requests from this IP, please try again later.'
    );
  } catch (error) {
    logger.error('Failed to get performance settings for rate limiting, using defaults:', error);
    return createRateLimit(
      15 * 60 * 1000, // 15 minutes
      100, // Default fallback
      'Too many requests from this IP, please try again later.'
    );
  }
};

// General rate limiting (will be updated dynamically)
export const generalRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests per window (will be updated)
  'Too many requests from this IP, please try again later.'
);

// Admin rate limiting (more lenient for admin operations)
export const adminRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  500, // 500 requests per window for admin operations
  'Too many admin requests from this IP, please try again later.'
);

// Dynamic authentication rate limiting based on security settings
export const createDynamicAuthRateLimit = async () => {
  try {
    const securitySettings = await settingsService.getSecuritySettings();
    const maxAttempts = Math.min(securitySettings.maxLoginAttempts, 10); // Cap at 10 for safety

    return createRateLimit(
      15 * 60 * 1000, // 15 minutes
      maxAttempts,
      'Too many authentication attempts, please try again later.'
    );
  } catch (error) {
    logger.error('Failed to get security settings for auth rate limiting, using defaults:', error);
    return createRateLimit(
      15 * 60 * 1000, // 15 minutes
      5, // Default fallback
      'Too many authentication attempts, please try again later.'
    );
  }
};

// Authentication rate limiting (will be updated dynamically)
export const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts per window (will be updated)
  'Too many authentication attempts, please try again later.'
);

// Order creation rate limiting
export const orderRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 orders per window
  'Too many order creation attempts, please try again later.'
);

// Slow down middleware for repeated requests
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: () => 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
});

// Per-user rate limiting middleware
export const createUserRateLimit = (
  identifier: string,
  maxRequests: number,
  windowMs: number = 15 * 60 * 1000,
  message?: string
) => {
  const store = new UserRateLimitStore(windowMs, `user_rate_limit_${identifier}`);

  return async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    // Skip if user is not authenticated
    if (!req.user?.id) {
      return next();
    }

    const userId = req.user.id;

    try {
      const { totalHits, resetTime } = await store.increment(userId, identifier);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, maxRequests - totalHits).toString(),
        'X-RateLimit-Reset': resetTime.toISOString(),
        'X-RateLimit-User': 'true',
      });

      if (totalHits > maxRequests) {
        logSecurityEvent('USER_RATE_LIMIT_EXCEEDED', {
          userId,
          identifier,
          totalHits,
          maxRequests,
          path: req.path,
          method: req.method,
          userAgent: req.get('User-Agent'),
        }, req);

        return res.status(429).json({
          success: false,
          error: message || 'Too many requests from this user, please try again later.',
          retryAfter: Math.ceil(windowMs / 1000),
        });
      }

      next();
    } catch (error) {
      logger.error('User rate limiting error:', error);
      // On error, allow request but log the issue
      next();
    }
  };
};

// Dynamic per-user rate limiting based on database settings
export const createDynamicUserRateLimit = async (identifier: string) => {
  try {
    const rateLimitSettings = await settingsService.getRateLimitSettings();

    let maxRequests: number;
    switch (identifier) {
      case 'general':
        maxRequests = rateLimitSettings.generalRequestsPerWindow;
        break;
      case 'admin':
        maxRequests = rateLimitSettings.adminRequestsPerWindow;
        break;
      case 'auth':
        maxRequests = rateLimitSettings.authRequestsPerWindow;
        break;
      case 'order':
        maxRequests = rateLimitSettings.orderRequestsPerWindow;
        break;
      default:
        maxRequests = 100; // Default fallback
    }

    return createUserRateLimit(
      identifier,
      maxRequests,
      15 * 60 * 1000, // 15 minutes
      `Too many ${identifier} requests from this user, please try again later.`
    );
  } catch (error) {
    logger.error(`Failed to get rate limit settings for ${identifier}, using defaults:`, error);
    return createUserRateLimit(identifier, 100, 15 * 60 * 1000);
  }
};

// Role-based per-user rate limiting
export const createRoleBasedUserRateLimit = (
  identifier: string,
  userLimits: { user: number; admin: number },
  windowMs: number = 15 * 60 * 1000
) => {
  const userStore = new UserRateLimitStore(windowMs, `user_rate_limit_${identifier}_user`);
  const adminStore = new UserRateLimitStore(windowMs, `user_rate_limit_${identifier}_admin`);

  return async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    // Skip if user is not authenticated
    if (!req.user?.id) {
      return next();
    }

    const userId = req.user.id;
    const userRole = req.user.role;
    const isAdmin = userRole === 'admin';
    const maxRequests = isAdmin ? userLimits.admin : userLimits.user;
    const store = isAdmin ? adminStore : userStore;

    try {
      const { totalHits, resetTime } = await store.increment(userId, `${identifier}_${userRole}`);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, maxRequests - totalHits).toString(),
        'X-RateLimit-Reset': resetTime.toISOString(),
        'X-RateLimit-User': 'true',
        'X-RateLimit-Role': userRole,
      });

      if (totalHits > maxRequests) {
        logSecurityEvent('USER_RATE_LIMIT_EXCEEDED', {
          userId,
          userRole,
          identifier,
          totalHits,
          maxRequests,
          path: req.path,
          method: req.method,
          userAgent: req.get('User-Agent'),
        }, req);

        return res.status(429).json({
          success: false,
          error: `Too many ${identifier} requests from this ${userRole}, please try again later.`,
          retryAfter: Math.ceil(windowMs / 1000),
        });
      }

      next();
    } catch (error) {
      logger.error('Role-based user rate limiting error:', error);
      // On error, allow request but log the issue
      next();
    }
  };
};

// Input validation middleware
export const validateInput = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logSecurityEvent('VALIDATION_FAILED', {
        errors: errors.array(),
        body: req.body,
        path: req.path,
      }, req);
      
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
        message: errors.array().map(err => err.msg).join(', '),
      });
    }
    
    next();
  };
};

// Common validation rules
export const emailValidation = body('email')
  .isEmail()
  .normalizeEmail()
  .isLength({ max: 255 })
  .withMessage('Valid email is required');

// Dynamic password validation based on security settings
export const createDynamicPasswordValidation = async () => {
  try {
    const securitySettings = await settingsService.getSecuritySettings();

    return body('password')
      .isLength({ min: securitySettings.passwordMinLength, max: 128 })
      .custom(async (password) => {
        const errors = [];

        if (securitySettings.passwordRequireLowercase && !/(?=.*[a-z])/.test(password)) {
          errors.push('lowercase letter');
        }
        if (securitySettings.passwordRequireUppercase && !/(?=.*[A-Z])/.test(password)) {
          errors.push('uppercase letter');
        }
        if (securitySettings.passwordRequireNumbers && !/(?=.*\d)/.test(password)) {
          errors.push('number');
        }
        if (securitySettings.passwordRequireSymbols && !/(?=.*[@$!%*?&])/.test(password)) {
          errors.push('special character (@$!%*?&)');
        }

        if (errors.length > 0) {
          throw new Error(`Password must contain at least one ${errors.join(', ')}`);
        }

        return true;
      })
      .withMessage(`Password must be at least ${securitySettings.passwordMinLength} characters`);
  } catch (error) {
    logger.error('Failed to get security settings for password validation, using defaults:', error);
    return body('password')
      .isLength({ min: 8, max: 128 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must be 8-128 characters with uppercase, lowercase, number, and special character');
  }
};

// Static password validation (fallback)
export const passwordValidation = body('password')
  .isLength({ min: 8, max: 128 })
  .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .withMessage('Password must be 8-128 characters with uppercase, lowercase, number, and special character');

export const telegramIdValidation = body('telegramId')
  .matches(/^[a-zA-Z0-9_]{5,32}$/)
  .withMessage('Telegram ID must be 5-32 characters, alphanumeric and underscores only');

export const amountValidation = body('amount')
  .isFloat({ min: 0.01, max: 1000000 })
  .withMessage('Amount must be between 0.01 and 1,000,000');

// Dynamic amount validation that uses system settings
export const dynamicAmountValidation = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { amount } = req.body;

    if (!amount) {
      return res.status(400).json({
        success: false,
        error: 'Amount is required',
      });
    }

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be a valid positive number',
      });
    }

    // Get payment settings from system settings
    const paymentSettings = await settingsService.getPaymentSettings();
    const minAmount = paymentSettings.minOrderAmount;
    const maxAmount = paymentSettings.maxOrderAmount;

    if (numAmount < minAmount) {
      return res.status(400).json({
        success: false,
        error: `Amount must be at least ${minAmount}`,
      });
    }

    if (numAmount > maxAmount) {
      return res.status(400).json({
        success: false,
        error: `Amount must not exceed ${maxAmount}`,
      });
    }

    next();
  } catch (error) {
    logger.error('Dynamic amount validation error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to validate amount',
    });
  }
};

export const memoValidation = body('memo')
  .optional()
  .isLength({ max: 500 })
  .trim()
  .escape()
  .withMessage('Memo must be less than 500 characters');

// IP whitelist middleware
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    const clientIP = req.ip || req.connection.remoteAddress || '';

    if (!allowedIPs.includes(clientIP)) {
      logSecurityEvent('IP_NOT_WHITELISTED', { clientIP, allowedIPs }, req);
      return res.status(403).json({
        success: false,
        error: 'Access denied',
      });
    }
    
    next();
  };
};

// Request size limiting
export const requestSizeLimit = (maxSize: number) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    const contentLength = parseInt(req.get('content-length') || '0');
    
    if (contentLength > maxSize) {
      logSecurityEvent('REQUEST_SIZE_EXCEEDED', {
        contentLength,
        maxSize,
        path: req.path,
      }, req);
      
      return res.status(413).json({
        success: false,
        error: 'Request entity too large',
      });
    }
    
    next();
  };
};

// Security headers middleware
export const securityHeaders = (_req: Request, res: Response, next: NextFunction): void => {
  // Remove server information
  res.removeHeader('X-Powered-By');

  // Add custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  next();
};

// Suspicious activity detection
export const detectSuspiciousActivity = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  const clientIP = req.ip;
  const userAgent = req.get('User-Agent') || '';
  const path = req.path;
  
  // Check for common attack patterns
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /eval\(/i,  // Code injection
  ];
  
  const requestData = JSON.stringify(req.body) + req.url + req.get('User-Agent');
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestData)) {
      logSecurityEvent('SUSPICIOUS_ACTIVITY_DETECTED', {
        pattern: pattern.toString(),
        path,
        method: req.method,
        userAgent,
        body: req.body,
      }, req);
      
      // Temporarily block this IP
      await redis.setEx(`blocked_ip:${clientIP}`, 3600, 'suspicious_activity'); // 1 hour block
      
      return res.status(403).json({
        success: false,
        error: 'Suspicious activity detected',
      });
    }
  }
  
  next();
};

// Check if IP is blocked
export const checkBlockedIP = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  const clientIP = req.ip;
  
  try {
    const isBlocked = await redis.get(`blocked_ip:${clientIP}`);
    
    if (isBlocked) {
      logSecurityEvent('BLOCKED_IP_ACCESS_ATTEMPT', { clientIP }, req);
      return res.status(403).json({
        success: false,
        error: 'Access temporarily restricted',
      });
    }
  } catch (error) {
    logger.error('Error checking blocked IP:', error);
  }
  
  next();
};

// Honeypot middleware (trap for bots)
export const honeypot = (req: Request, res: Response, next: NextFunction): void | Response => {
  // Check for honeypot field in forms
  if (req.body && req.body.website) {
    logSecurityEvent('HONEYPOT_TRIGGERED', {
      honeypotValue: req.body.website,
      path: req.path,
    }, req);

    // Silently reject the request
    return res.status(200).json({
      success: true,
      message: 'Request processed',
    });
  }

  next();
};

// Per-user rate limiting instances (will be updated dynamically)
export const userGeneralRateLimit = createUserRateLimit(
  'general',
  100, // 100 requests per window (will be updated)
  15 * 60 * 1000,
  'Too many requests from this user, please try again later.'
);

export const userAuthRateLimit = createUserRateLimit(
  'auth',
  5, // 5 attempts per window (will be updated)
  15 * 60 * 1000,
  'Too many authentication attempts from this user, please try again later.'
);

export const userOrderRateLimit = createUserRateLimit(
  'order',
  5, // 5 orders per window (will be updated)
  15 * 60 * 1000,
  'Too many order creation attempts from this user, please try again later.'
);

// Role-based per-user rate limiting for admin operations
export const userAdminRateLimit = createRoleBasedUserRateLimit(
  'admin',
  { user: 50, admin: 500 }, // Different limits for users vs admins
  15 * 60 * 1000
);

// Function to update per-user rate limiting based on database settings
export const updateUserRateLimits = async (): Promise<void> => {
  try {
    const rateLimitSettings = await settingsService.getRateLimitSettings();

    logger.info('Updating per-user rate limits with database settings:', {
      userGeneralRequestsPerWindow: rateLimitSettings.userGeneralRequestsPerWindow,
      userAdminRequestsPerWindow: rateLimitSettings.userAdminRequestsPerWindow,
      userOrderRequestsPerWindow: rateLimitSettings.userOrderRequestsPerWindow,
      userAuthRequestsPerWindow: rateLimitSettings.userAuthRequestsPerWindow,
    });

    // Note: Since the rate limiters are already created, we would need to implement
    // a dynamic update mechanism. For now, this function serves as a placeholder
    // for future implementation of hot-reloading rate limits.

    logSecurityEvent('USER_RATE_LIMITS_UPDATED', {
      userGeneralRequestsPerWindow: rateLimitSettings.userGeneralRequestsPerWindow,
      userAdminRequestsPerWindow: rateLimitSettings.userAdminRequestsPerWindow,
      userOrderRequestsPerWindow: rateLimitSettings.userOrderRequestsPerWindow,
      userAuthRequestsPerWindow: rateLimitSettings.userAuthRequestsPerWindow,
    });

  } catch (error) {
    logger.error('Failed to update per-user rate limits:', error);
  }
};
