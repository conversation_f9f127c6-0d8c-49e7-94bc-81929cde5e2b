'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { publicSettingsApi, authSettingsApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

// PUBLIC SETTINGS - Minimal data for unauthenticated users
interface PublicPaymentSettings {
  supportedCurrencies: string[];
  minOrderAmount: number;
  maxOrderAmount: number;
  paymentTimeoutMinutes: number;
}

interface PublicVoucherSettings {
  defaultExpiryDays: number;
  maxVouchersPerOrder: number;
  allowVoucherStacking: boolean;
}

interface PublicSecuritySettings {
  passwordMinLength: number;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  passwordRequireUppercase: boolean;
}

interface PublicSystemSettings {
  maintenanceMode: boolean;
  maintenanceMessage: string;
}

// AUTHENTICATED SETTINGS - Additional data for authenticated users
interface AuthenticatedPaymentSettings extends PublicPaymentSettings {
  // No additional payment settings in current schema
}

interface AuthenticatedVoucherSettings extends PublicVoucherSettings {
  // No additional voucher settings in current schema
}

interface AuthenticatedSecuritySettings extends PublicSecuritySettings {
  enableTwoFactor: boolean;
  maxLoginAttempts: number;
  lockoutDurationMinutes: number;
}

interface AuthenticatedSystemSettings extends PublicSystemSettings {
  apiVersion: string;
  maxFileUploadSize: number;
  enableAnalytics: boolean;
  logLevel: string;
}

interface AuthenticatedNotificationSettings {
  enableEmailNotifications: boolean;
  enableOrderConfirmations: boolean;
  enableVoucherNotifications: boolean;
  enableSecurityAlerts: boolean;
}

interface PublicSettings {
  payment: PublicPaymentSettings;
  voucher: PublicVoucherSettings;
  security: PublicSecuritySettings;
  system: PublicSystemSettings;
}

interface AuthenticatedSettings {
  payment: AuthenticatedPaymentSettings;
  voucher: AuthenticatedVoucherSettings;
  security: AuthenticatedSecuritySettings;
  system: AuthenticatedSystemSettings;
  notifications: AuthenticatedNotificationSettings;
}

interface SettingsContextType {
  // Public settings (always available)
  publicSettings: PublicSettings | null;
  // Authenticated settings (only for logged-in users)
  authenticatedSettings: AuthenticatedSettings | null;
  loading: boolean;
  error: string | null;
  isConnected: boolean; // WebSocket connection status
  refreshSettings: () => Promise<void>;
  // Convenience getters that return appropriate settings based on auth status
  getPaymentSettings: () => PublicPaymentSettings | AuthenticatedPaymentSettings | null;
  getVoucherSettings: () => PublicVoucherSettings | AuthenticatedVoucherSettings | null;
  getSecuritySettings: () => PublicSecuritySettings | AuthenticatedSecuritySettings | null;
  getSystemSettings: () => PublicSystemSettings | AuthenticatedSystemSettings | null;
  getNotificationSettings: () => AuthenticatedNotificationSettings | null;
}

const defaultPublicSettings: PublicSettings = {
  payment: {
    supportedCurrencies: ['TON', 'USD', 'EUR'],
    minOrderAmount: 0.01,
    maxOrderAmount: 1000000,
    paymentTimeoutMinutes: 30,
  },
  voucher: {
    defaultExpiryDays: 365,
    maxVouchersPerOrder: 10,
    allowVoucherStacking: false,
  },
  security: {
    passwordMinLength: 8,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: true,
    passwordRequireUppercase: true,
  },
  system: {
    maintenanceMode: false,
    maintenanceMessage: 'System is under maintenance. Please try again later.',
  },
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [publicSettings, setPublicSettings] = useState<PublicSettings | null>(null);
  const [authenticatedSettings, setAuthenticatedSettings] = useState<AuthenticatedSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [ws, setWs] = useState<WebSocket | null>(null);

  // Fetch public settings (always available)
  const fetchPublicSettings = async () => {
    try {
      const response = await publicSettingsApi.getAll();
      if (response.success) {
        setPublicSettings(response.data.settings);
      } else {
        console.warn('Failed to fetch public settings, using defaults:', response.error);
        setPublicSettings(defaultPublicSettings);
        setError(response.error || 'Failed to fetch public settings');
      }
    } catch (error) {
      console.error('Error fetching public settings:', error);
      setPublicSettings(defaultPublicSettings);
      setError('Failed to fetch public settings');
    }
  };

  // Fetch authenticated settings (only for logged-in users)
  const fetchAuthenticatedSettings = async () => {
    if (!user) {
      setAuthenticatedSettings(null);
      return;
    }

    try {
      const response = await authSettingsApi.getAll();
      if (response.success) {
        setAuthenticatedSettings(response.data.settings);
      } else {
        console.warn('Failed to fetch authenticated settings:', response.error);
        setError(response.error || 'Failed to fetch authenticated settings');
      }
    } catch (error) {
      console.error('Error fetching authenticated settings:', error);
      setError('Failed to fetch authenticated settings');
    }
  };

  const refreshSettings = async () => {
    setLoading(true);
    setError(null);

    await Promise.all([
      fetchPublicSettings(),
      fetchAuthenticatedSettings()
    ]);

    setLoading(false);
  };

  // WebSocket connection for real-time updates (admin only)
  const connectWebSocket = useCallback(() => {
    if (!user || user.role !== 'admin') return;

    const token = localStorage.getItem('token');
    if (!token) return;

    try {
      const wsUrl = `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'}/ws/admin/settings?token=${token}`;
      const websocket = new WebSocket(wsUrl);

      websocket.onopen = () => {
        console.log('WebSocket connected for real-time settings updates');
        setIsConnected(true);
      };

      websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'SETTINGS_UPDATE') {
            console.log('Real-time settings update received:', message.data);
            // Refresh settings to get the latest data
            refreshSettings();
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      websocket.onclose = () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        // Attempt to reconnect after 5 seconds
        setTimeout(connectWebSocket, 5000);
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
      };

      setWs(websocket);
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }, [user, refreshSettings]);

  // Convenience getters that return appropriate settings based on auth status
  const getPaymentSettings = (): PublicPaymentSettings | AuthenticatedPaymentSettings | null => {
    return authenticatedSettings?.payment || publicSettings?.payment || null;
  };

  const getVoucherSettings = (): PublicVoucherSettings | AuthenticatedVoucherSettings | null => {
    return authenticatedSettings?.voucher || publicSettings?.voucher || null;
  };

  const getSecuritySettings = (): PublicSecuritySettings | AuthenticatedSecuritySettings | null => {
    return authenticatedSettings?.security || publicSettings?.security || null;
  };

  const getSystemSettings = (): PublicSystemSettings | AuthenticatedSystemSettings | null => {
    return authenticatedSettings?.system || publicSettings?.system || null;
  };

  const getNotificationSettings = (): AuthenticatedNotificationSettings | null => {
    return authenticatedSettings?.notifications || null;
  };

  // Initial load
  useEffect(() => {
    refreshSettings();
  }, []);

  // Connect WebSocket when user changes
  useEffect(() => {
    if (user && user.role === 'admin') {
      connectWebSocket();
    } else if (ws) {
      ws.close();
      setWs(null);
      setIsConnected(false);
    }

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [user, connectWebSocket]);

  // Cleanup WebSocket on unmount
  useEffect(() => {
    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [ws]);

  // Refresh settings every 5 minutes for non-admin users (admin gets real-time updates)
  useEffect(() => {
    if (user?.role !== 'admin') {
      const interval = setInterval(() => {
        refreshSettings();
      }, 5 * 60 * 1000); // 5 minutes

      return () => clearInterval(interval);
    }
    return undefined;
  }, [user, refreshSettings]);

  const value: SettingsContextType = {
    publicSettings,
    authenticatedSettings,
    loading,
    error,
    isConnected,
    refreshSettings,
    getPaymentSettings,
    getVoucherSettings,
    getSecuritySettings,
    getSystemSettings,
    getNotificationSettings,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
