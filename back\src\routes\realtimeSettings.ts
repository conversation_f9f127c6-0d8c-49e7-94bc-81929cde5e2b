import { Router } from 'express';
import { getAuthenticatedSettings } from '../controllers/realtimeSettingsController';
import { authenticate, requireUser } from '../middleware/auth';
import { validateInput } from '../middleware/security';

const router = Router();

/**
 * SECURE AUTHENTICATED SETTINGS ROUTES
 * Replaces vulnerable public settings API with authenticated access
 * Provides real-time updates via WebSocket for admin users
 */

// All routes require authentication
router.use(authenticate);
router.use(requireUser);

/**
 * @route   GET /api/v1/auth/settings
 * @desc    Get authenticated settings for frontend (secure replacement for public settings)
 * @access  Private (Authenticated Users)
 */
router.get('/', getAuthenticatedSettings);

export default router;
