import { Request, Response } from 'express';
import { 
  logErrorWithRequest, 
  logBusinessError, 
  logValidationError, 
  logCriticalError,
  sanitizeForLogging 
} from '../config/logger';

/**
 * Standard error response interface
 */
interface ErrorResponse {
  success: false;
  error: string;
  errorId?: string;
  details?: any;
  code?: string;
}

/**
 * Error handling utility for controllers
 * Provides consistent error logging and response formatting
 */
export class ControllerErrorHandler {
  /**
   * Handle general controller errors
   */
  static handleError(
    error: Error,
    req: Request,
    res: Response,
    operation: string,
    context?: any
  ): Response {
    const errorId = logErrorWithRequest(error, req, {
      operation,
      controller: req.route?.path || req.path,
      ...sanitizeForLogging(context),
    });

    const isDevelopment = process.env.NODE_ENV === 'development';

    const response: ErrorResponse = {
      success: false,
      error: isDevelopment ? error.message : 'An error occurred',
      errorId,
    };

    // Include stack trace only in development
    if (isDevelopment && error.stack) {
      response.details = { stack: error.stack };
    }

    return res.status(500).json(response);
  }

  /**
   * Handle business logic errors
   */
  static handleBusinessError(
    error: Error,
    req: Request,
    res: Response,
    operation: string,
    businessContext?: any,
    statusCode: number = 400
  ): Response {
    const errorId = logBusinessError(operation, error, businessContext, req);

    const response: ErrorResponse = {
      success: false,
      error: error.message,
      errorId,
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Handle validation errors
   */
  static handleValidationError(
    errors: any[],
    req: Request,
    res: Response,
    context?: any
  ): Response {
    logValidationError(errors, req, context);

    const response: ErrorResponse = {
      success: false,
      error: 'Validation failed',
      details: errors.map(err => ({
        field: err.param || err.path,
        message: err.msg || err.message,
        value: err.value,
      })),
    };

    return res.status(400).json(response);
  }

  /**
   * Handle critical errors that require immediate attention
   */
  static handleCriticalError(
    error: Error,
    req: Request,
    res: Response,
    operation: string,
    context?: any
  ): Response {
    const errorId = logCriticalError(error, operation, context, req);

    const response: ErrorResponse = {
      success: false,
      error: 'A critical error occurred. Please contact support.',
      errorId,
      code: 'CRITICAL_ERROR',
    };

    return res.status(500).json(response);
  }

  /**
   * Handle authentication errors
   */
  static handleAuthError(
    message: string,
    req: Request,
    res: Response,
    statusCode: number = 401,
    context?: any
  ): Response {
    const errorId = logErrorWithRequest(
      new Error(message),
      req,
      { authError: true, ...sanitizeForLogging(context) }
    );

    const response: ErrorResponse = {
      success: false,
      error: message,
      errorId,
      code: 'AUTH_ERROR',
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Handle not found errors
   */
  static handleNotFoundError(
    resource: string,
    req: Request,
    res: Response,
    context?: any
  ): Response {
    const message = `${resource} not found`;
    const errorId = logErrorWithRequest(
      new Error(message),
      req,
      { notFound: true, resource, ...sanitizeForLogging(context) }
    );

    const response: ErrorResponse = {
      success: false,
      error: message,
      errorId,
      code: 'NOT_FOUND',
    };

    return res.status(404).json(response);
  }

  /**
   * Handle permission errors
   */
  static handlePermissionError(
    req: Request,
    res: Response,
    context?: any
  ): Response {
    const message = 'Insufficient permissions';
    const errorId = logErrorWithRequest(
      new Error(message),
      req,
      { permissionError: true, ...sanitizeForLogging(context) }
    );

    const response: ErrorResponse = {
      success: false,
      error: message,
      errorId,
      code: 'PERMISSION_DENIED',
    };

    return res.status(403).json(response);
  }

  /**
   * Handle rate limit errors
   */
  static handleRateLimitError(
    req: Request,
    res: Response,
    retryAfter?: number
  ): Response {
    const message = 'Rate limit exceeded';
    const errorId = logErrorWithRequest(
      new Error(message),
      req,
      { rateLimitError: true, retryAfter }
    );

    const response: ErrorResponse = {
      success: false,
      error: message,
      errorId,
      code: 'RATE_LIMIT_EXCEEDED',
    };

    if (retryAfter) {
      res.setHeader('Retry-After', retryAfter.toString());
    }

    return res.status(429).json(response);
  }
}

/**
 * Async error wrapper for controller methods
 * Automatically catches and handles async errors
 */
export const asyncErrorHandler = (
  fn: (req: Request, res: Response, next?: any) => Promise<any>
) => {
  return (req: Request, res: Response, next: any) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      // If response hasn't been sent yet, handle the error
      if (!res.headersSent) {
        ControllerErrorHandler.handleError(
          error,
          req,
          res,
          'ASYNC_OPERATION',
          { asyncError: true }
        );
      } else {
        // If response was already sent, just log the error
        logErrorWithRequest(error, req, { 
          asyncError: true, 
          responseAlreadySent: true 
        });
      }
    });
  };
};

/**
 * Database error handler
 * Handles common database errors with appropriate responses
 */
export const handleDatabaseError = (
  error: any,
  req: Request,
  res: Response,
  operation: string,
  context?: any
): Response => {
  // Check for common database errors
  if (error.code === '23505') { // Unique constraint violation
    return ControllerErrorHandler.handleBusinessError(
      new Error('Resource already exists'),
      req,
      res,
      operation,
      { ...context, dbError: 'unique_violation' },
      409
    );
  }

  if (error.code === '23503') { // Foreign key constraint violation
    return ControllerErrorHandler.handleBusinessError(
      new Error('Referenced resource not found'),
      req,
      res,
      operation,
      { ...context, dbError: 'foreign_key_violation' },
      400
    );
  }

  if (error.code === '23502') { // Not null constraint violation
    return ControllerErrorHandler.handleBusinessError(
      new Error('Required field missing'),
      req,
      res,
      operation,
      { ...context, dbError: 'not_null_violation' },
      400
    );
  }

  // For other database errors, treat as general error
  return ControllerErrorHandler.handleError(
    error,
    req,
    res,
    operation,
    { ...context, dbError: true }
  );
};
