'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Clock, CheckCircle, AlertCircle, CreditCard } from 'lucide-react';
import { ordersApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { useTonConnect } from '@/contexts/TonConnectContext';
import WalletConnection from '@/components/wallet/WalletConnection';
import TonPayment from '@/components/payment/TonPayment';
import { toast } from 'react-hot-toast';

interface Order {
  id: string;
  status: string;
  amount: string;
  currency: string;
  memo?: string;
  paymentAddress: string;
  paymentExpiresAt: string;
  transactionHash?: string;
  createdAt: string;
  updatedAt: string;
  voucher?: {
    id: string;
    code: string;
    status: string;
  };
}

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { connected } = useTonConnect();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const orderId = params.id as string;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    fetchOrder();
  }, [user, orderId]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await ordersApi.getById(orderId);
      
      if (response.success && response.data?.order) {
        setOrder(response.data.order);
      } else {
        setError('Order not found');
      }
    } catch (err) {
      console.error('Failed to fetch order:', err);
      setError('Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentComplete = (transactionHash: string) => {
    toast.success('Payment completed successfully!');
    fetchOrder(); // Refresh order data
  };

  const handlePaymentFailed = (error: string) => {
    toast.error(`Payment failed: ${error}`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success-500" />;
      case 'failed':
      case 'cancelled':
        return <AlertCircle className="h-5 w-5 text-error-500" />;
      case 'payment_pending':
      case 'paid':
        return <Clock className="h-5 w-5 text-warning-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Waiting for payment';
      case 'payment_pending':
        return 'Payment sent - confirming';
      case 'paid':
        return 'Payment confirmed';
      case 'completed':
        return 'Order completed';
      case 'cancelled':
        return 'Order cancelled';
      case 'failed':
        return 'Payment failed';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-success-600 bg-success-50 border-success-200';
      case 'failed':
      case 'cancelled':
        return 'text-error-600 bg-error-50 border-error-200';
      case 'payment_pending':
      case 'paid':
        return 'text-warning-600 bg-warning-50 border-warning-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner-lg mx-auto mb-4" />
          <p className="text-gray-600">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-error-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="btn-primary"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Order Details</h1>
              <p className="text-gray-600">Order #{order.id.slice(0, 8)}</p>
            </div>
            
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
              {getStatusIcon(order.status)}
              <span className="ml-2">{getStatusText(order.status)}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Summary */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
              </div>
              <div className="card-body">
                <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Amount</dt>
                    <dd className="mt-1 text-lg font-semibold text-gray-900">
                      {order.amount} {order.currency}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Status</dt>
                    <dd className="mt-1">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {getStatusText(order.status)}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(order.createdAt).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(order.updatedAt).toLocaleString()}
                    </dd>
                  </div>
                  {order.memo && (
                    <div className="sm:col-span-2">
                      <dt className="text-sm font-medium text-gray-500">Memo</dt>
                      <dd className="mt-1 text-sm text-gray-900">{order.memo}</dd>
                    </div>
                  )}
                  {order.transactionHash && (
                    <div className="sm:col-span-2">
                      <dt className="text-sm font-medium text-gray-500">Transaction Hash</dt>
                      <dd className="mt-1 text-sm font-mono text-gray-900 break-all">
                        {order.transactionHash}
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>

            {/* Voucher Information */}
            {order.voucher && (
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-medium text-gray-900">Voucher</h2>
                </div>
                <div className="card-body">
                  <div className="bg-gradient-to-r from-ton-500 to-ton-600 rounded-lg p-6 text-white">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold">TON Voucher</h3>
                      <span className="text-sm opacity-90">#{order.voucher.id.slice(0, 8)}</span>
                    </div>
                    <div className="text-center">
                      <p className="text-sm opacity-90 mb-2">Voucher Code</p>
                      <p className="text-2xl font-bold tracking-wider">{order.voucher.code}</p>
                    </div>
                    <div className="mt-4 pt-4 border-t border-white/20">
                      <div className="flex justify-between text-sm">
                        <span>Status:</span>
                        <span className="capitalize">{order.voucher.status}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Payment Section */}
          <div className="space-y-6">
            {/* Wallet Connection */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Wallet</h2>
              </div>
              <div className="card-body">
                <WalletConnection showBalance={true} showDisconnect={false} />
              </div>
            </div>

            {/* Payment */}
            {['pending', 'payment_pending'].includes(order.status) && (
              <TonPayment
                orderId={order.id}
                amount={order.amount}
                currency={order.currency}
                paymentAddress={order.paymentAddress}
                expiresAt={order.paymentExpiresAt}
                onPaymentComplete={handlePaymentComplete}
                onPaymentFailed={handlePaymentFailed}
              />
            )}

            {/* Completed Payment Info */}
            {['paid', 'completed'].includes(order.status) && (
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-medium text-gray-900">Payment Completed</h2>
                </div>
                <div className="card-body">
                  <div className="text-center">
                    <CheckCircle className="h-12 w-12 text-success-500 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-900 mb-2">
                      Payment Successful!
                    </p>
                    <p className="text-sm text-gray-600 mb-4">
                      Your voucher has been generated and is ready to use.
                    </p>
                    {order.voucher && (
                      <button
                        onClick={() => router.push('/dashboard/vouchers')}
                        className="btn-primary"
                      >
                        View Vouchers
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
