import { Request, Response } from 'express';
import { executeQuery } from '../config/database';
import { logger } from '../config/logger';

// Get user settings
export const getUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const settingsResult = await executeQuery(
      `SELECT 
        notification_email, notification_push, notification_order_updates,
        notification_promotions, notification_security,
        privacy_profile_visibility, privacy_data_sharing, privacy_analytics,
        appearance_theme, appearance_language
       FROM user_settings 
       WHERE user_id = $1`,
      [userId]
    );

    let settings;
    if (settingsResult.rows.length === 0) {
      // Create default settings if none exist
      await executeQuery(
        `INSERT INTO user_settings (
          user_id, notification_email, notification_push, notification_order_updates,
          notification_promotions, notification_security,
          privacy_profile_visibility, privacy_data_sharing, privacy_analytics,
          appearance_theme, appearance_language
        ) VALUES ($1, true, true, true, false, true, 'private', false, true, 'system', 'en')`,
        [userId]
      );

      settings = {
        notifications: {
          email: true,
          push: true,
          orderUpdates: true,
          promotions: false,
          security: true,
        },
        privacy: {
          profileVisibility: 'private',
          dataSharing: false,
          analytics: true,
        },
        appearance: {
          theme: 'system',
          language: 'en',
        },
      };
    } else {
      const row = settingsResult.rows[0];
      settings = {
        notifications: {
          email: row.notification_email,
          push: row.notification_push,
          orderUpdates: row.notification_order_updates,
          promotions: row.notification_promotions,
          security: row.notification_security,
        },
        privacy: {
          profileVisibility: row.privacy_profile_visibility,
          dataSharing: row.privacy_data_sharing,
          analytics: row.privacy_analytics,
        },
        appearance: {
          theme: row.appearance_theme,
          language: row.appearance_language,
        },
      };
    }

    res.json({
      success: true,
      data: { settings },
    });
  } catch (error) {
    logger.error('Get user settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user settings',
    });
  }
};

// Update user settings
export const updateUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { notifications, privacy, appearance } = req.body;

    // Update settings
    await executeQuery(
      `UPDATE user_settings SET
        notification_email = $2,
        notification_push = $3,
        notification_order_updates = $4,
        notification_promotions = $5,
        notification_security = $6,
        privacy_profile_visibility = $7,
        privacy_data_sharing = $8,
        privacy_analytics = $9,
        appearance_theme = $10,
        appearance_language = $11,
        updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $1`,
      [
        userId,
        notifications.email,
        notifications.push,
        notifications.orderUpdates,
        notifications.promotions,
        notifications.security,
        privacy.profileVisibility,
        privacy.dataSharing,
        privacy.analytics,
        appearance.theme,
        appearance.language,
      ]
    );

    res.json({
      success: true,
      message: 'Settings updated successfully',
    });
  } catch (error) {
    logger.error('Update user settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update settings',
    });
  }
};

// Export user data
export const exportUserData = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get user data
    const userResult = await executeQuery(
      `SELECT id, email, telegram_id, email_verified, role, created_at, updated_at, last_login
       FROM users WHERE id = $1`,
      [userId]
    );

    const ordersResult = await executeQuery(
      `SELECT id, amount, currency, status, created_at, updated_at
       FROM orders WHERE user_id = $1`,
      [userId]
    );

    const vouchersResult = await executeQuery(
      `SELECT v.id, v.code, v.status, v.created_at, v.expires_at, v.redeemed_at
       FROM vouchers v
       JOIN orders o ON v.order_id = o.id
       WHERE o.user_id = $1`,
      [userId]
    );

    const settingsResult = await executeQuery(
      `SELECT * FROM user_settings WHERE user_id = $1`,
      [userId]
    );

    const exportData = {
      user: userResult.rows[0],
      orders: ordersResult.rows,
      vouchers: vouchersResult.rows,
      settings: settingsResult.rows[0],
      exportedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: exportData,
      message: 'User data exported successfully',
    });
  } catch (error) {
    logger.error('Export user data error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export user data',
    });
  }
};

// Delete user account
export const deleteUserAccount = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Check for active orders or vouchers
    const activeItemsResult = await executeQuery(
      `SELECT 
        (SELECT COUNT(*) FROM orders WHERE user_id = $1 AND status IN ('pending', 'processing')) as active_orders,
        (SELECT COUNT(*) FROM vouchers v JOIN orders o ON v.order_id = o.id 
         WHERE o.user_id = $1 AND v.status = 'active') as active_vouchers`,
      [userId]
    );

    const { active_orders, active_vouchers } = activeItemsResult.rows[0];

    if (active_orders > 0 || active_vouchers > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete account with active orders or vouchers. Please contact support.',
      });
    }

    // Soft delete user
    await executeQuery(
      `UPDATE users 
       SET email = CONCAT('deleted_', id, '@deleted.local'),
           telegram_id = CONCAT('deleted_', id),
           password_hash = 'deleted',
           email_verified = false,
           email_verification_token = NULL,
           password_reset_token = NULL,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $1`,
      [userId]
    );

    // Invalidate all sessions
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE user_id = $1',
      [userId]
    );

    res.json({
      success: true,
      message: 'Account deleted successfully',
    });
  } catch (error) {
    logger.error('Delete user account error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete account',
    });
  }
};
