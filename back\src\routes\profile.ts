import { Router } from 'express';
import {
  getUserProfile,
  updateUserProfile,
  changePassword,
  getAccountSummary,
} from '../controllers/profileController';
import { validateInput } from '../middleware/security';
import { authenticate, requireUser } from '../middleware/auth';
import { body } from 'express-validator';

const router = Router();

// All profile routes require authentication
router.use(authenticate);
router.use(requireUser);

// Profile update validation
const profileUpdateValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('telegramId')
    .isLength({ min: 3, max: 100 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Telegram ID must be 3-100 characters and contain only letters, numbers, and underscores'),
];

// Password change validation
const passwordChangeValidation = [
  body('currentPassword')
    .isLength({ min: 1 })
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8, max: 128 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must be 8-128 characters and contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
];

/**
 * @route   GET /api/v1/profile
 * @desc    Get user profile with stats and recent activity
 * @access  Private (User)
 */
router.get('/', getUserProfile);

/**
 * @route   PUT /api/v1/profile
 * @desc    Update user profile
 * @access  Private (User)
 */
router.put(
  '/',
  validateInput(profileUpdateValidation),
  updateUserProfile
);

/**
 * @route   POST /api/v1/profile/change-password
 * @desc    Change user password
 * @access  Private (User)
 */
router.post(
  '/change-password',
  validateInput(passwordChangeValidation),
  changePassword
);

/**
 * @route   GET /api/v1/profile/summary
 * @desc    Get detailed account summary and statistics
 * @access  Private (User)
 */
router.get('/summary', getAccountSummary);

export default router;
