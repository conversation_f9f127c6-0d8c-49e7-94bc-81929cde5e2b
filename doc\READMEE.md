@@TON Voucher E-commerce Platform

## 🎯 Project Overview

A **security-first e-commerce platform** for selling premium game voucher codes with **TON cryptocurrency payments**. Built with modern web technologies and enterprise-grade security practices.

### Core Business Logic
1. **User Registration** → Email + Telegram ID + Memo field
2. **TON Payment** → Secure cryptocurrency transaction processing  
3. **Voucher Generation** → Server-side only, cryptographically secure codes
4. **Delivery** → Web UI + Email notification
5. **Redemption Tracking** → Prevent code reuse, maintain audit trail

## 🏗️ Architecture Stack

```
Frontend: Next.js + TypeScript + Tailwind CSS
Backend:  Express.js + TypeScript + PostgreSQL + Redis
Blockchain: TON SDK for payment processing
Email: Nodemailer + SMTP service
Security: Helmet, CORS, Rate Limiting, CSRF, Input Validation
```

## 📁 Project Structure

```
tonsite/
├── README.md                 # This file - project overview
├── frontend.md              # Frontend implementation tasks
├── backend.md               # Backend implementation tasks  
├── ton_integration.md       # TON blockchain integration tasks
├── database.md              # Database schema and setup tasks
├── implementation_guide.md  # Coding standards and best practices
├── milestones.md           # Progress tracking and phases
├── backend/                # Express.js API server
├── frontend/               # Next.js application
└── shared/                 # Shared types and utilities
```

## 🔐 Security Requirements (CRITICAL)

**Input Validation**: All inputs validated on both frontend AND backend  
**Rate Limiting**: Prevent abuse with configurable limits  
**CSRF Protection**: All forms protected against cross-site attacks  
**CORS Configuration**: Strict origin policies  
**Server-Side Vouchers**: Never generate voucher codes on client  
**Secure Storage**: Encrypted sensitive data, secure redemption tracking  

## 💾 Database Schema (PostgreSQL)

### Core Tables
- **users**: id, email, telegram_id, password_hash, email_verified, role
- **orders**: id, user_id, status, amount, currency, memo, payment_address, transaction_hash
- **vouchers**: id, order_id, code, status, redeemed_at, expires_at
- **transactions**: id, order_id, hash, from_address, to_address, amount, status, confirmations

### Relationships
- User → Orders (1:many)
- Order → Voucher (1:1)  
- Order → Transaction (1:1)

## 🪙 TON Integration Flow

1. **Payment Request**: Generate unique payment address per order
2. **Transaction Monitoring**: Watch blockchain for incoming payments
3. **Verification**: Validate amount, sender, memo fields
4. **Confirmation**: Wait for required confirmations
5. **Fulfillment**: Generate voucher code and send notifications

## 📋 Implementation Phases

### Phase 1: Foundation (Week 1-2)
- Database setup and schema
- Backend security middleware
- Frontend project initialization
- Development environment

### Phase 2: Authentication (Week 3-4)  
- User registration/login
- Email verification
- JWT token management
- Security hardening

### Phase 3: TON Integration (Week 5-6)
- Wallet connection
- Payment processing
- Transaction monitoring
- Error handling

### Phase 4: Voucher System (Week 7-8)
- Secure code generation
- Redemption tracking
- Email delivery
- User interface

### Phase 5: Order Management (Week 9-10)
- Purchase flow
- Order tracking
- Email notifications
- History management

### Phase 6: Admin Panel (Week 11-12)
- Admin authentication
- User/order/voucher management
- Analytics dashboard
- Bulk operations

### Phase 7: Security & Testing (Week 13-14)
- Security audit
- Comprehensive testing
- Performance optimization
- Bug fixes

### Phase 8: Deployment (Week 15-16)
- Production setup
- CI/CD pipeline
- Monitoring
- Launch preparation

## 🚀 Quick Start for AI Developers

### Starting Fresh on Any Component:

1. **Read this README.md** - Get project context
2. **Check milestones.md** - See current progress and next tasks
3. **Review implementation_guide.md** - Understand coding standards
4. **Open relevant task file**:
   - `frontend.md` for UI/UX work
   - `backend.md` for API/server work
   - `ton_integration.md` for blockchain features
   - `database.md` for data layer work

### Current Implementation Status:
Check `milestones.md` for the most up-to-date progress tracking.

## 🔧 Development Environment

### Required Tools
- Node.js v18+
- PostgreSQL v14+
- Redis v6+
- Git

### Environment Variables
```bash
# Backend
DATABASE_URL=postgresql://user:pass@localhost:5432/tonsite
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-key
TON_NETWORK=testnet
EMAIL_SMTP_HOST=smtp.gmail.com

# Frontend  
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_TON_NETWORK=testnet
```

## 🛡️ Security Checklist

- [ ] All inputs validated server-side
- [ ] Rate limiting on all endpoints
- [ ] CSRF tokens on all forms
- [ ] CORS properly configured
- [ ] Passwords properly hashed (bcrypt)
- [ ] JWT tokens with expiration
- [ ] SQL injection prevention (parameterized queries)
- [ ] XSS protection (input sanitization)
- [ ] HTTPS enforced in production
- [ ] Sensitive data encrypted at rest

## 📊 Key Metrics & Targets

- **Security Score**: A+ (required)
- **Test Coverage**: 80%+ (required)
- **Performance**: 90+ Lighthouse score
- **Uptime**: 99.9% target
- **Response Time**: <200ms API responses

## 🔄 Common Development Workflows

### Adding New Feature
1. Create feature branch from `main`
2. Update relevant task file (frontend.md, backend.md, etc.)
3. Implement with security-first approach
4. Write tests (unit + integration)
5. Update documentation
6. Security review
7. Merge to main

### Debugging Issues
1. Check logs for errors
2. Verify input validation
3. Test API endpoints independently
4. Check database constraints
5. Validate TON integration
6. Review security configurations

## 📞 Emergency Procedures

### If AI Gets Confused:
1. **Re-read this README.md** - Reset context
2. **Check milestones.md** - Understand current state
3. **Review implementation_guide.md** - Recall standards
4. **Ask user for clarification** - Don't assume

### If Implementation Breaks:
1. **Security First** - Ensure no vulnerabilities introduced
2. **Check Database** - Verify data integrity
3. **Test TON Integration** - Ensure payments still work
4. **Validate All Inputs** - Confirm validation still active
5. **Review Recent Changes** - Identify breaking changes

## 🎯 Success Criteria

**Functional**: Users can register, pay with TON, receive vouchers, redeem codes  
**Secure**: Passes security audit, no vulnerabilities  
**Performant**: Fast response times, efficient database queries  
**Reliable**: High uptime, error handling, transaction integrity  
**Maintainable**: Clean code, good documentation, comprehensive tests  

---

**Remember**: This is a **security-critical financial application**. Every decision should prioritize security over convenience. When in doubt, choose the more secure option and validate everything server-side.
