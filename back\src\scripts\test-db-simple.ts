#!/usr/bin/env ts-node

/**
 * Simple Database Test Script
 *
 * This script tests basic database operations to isolate the SASL authentication issue.
 */

import dotenv from 'dotenv';

// Load environment variables BEFORE importing anything that uses them
dotenv.config();

import { executeQuery } from '../config/database';

const testDatabase = async () => {
  try {
    console.log('🔍 Testing basic database operations...');
    
    // Test 1: Simple query
    console.log('\n1️⃣ Testing simple query...');
    const simpleResult = await executeQuery('SELECT NOW() as current_time');
    console.log('✅ Simple query successful:', simpleResult.rows[0]);
    
    // Test 2: Check if system_settings table exists
    console.log('\n2️⃣ Testing system_settings table access...');
    const tableCheck = await executeQuery(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_name = 'system_settings'
    `);
    console.log('✅ Table check successful:', tableCheck.rows[0]);
    
    // Test 3: Count settings
    console.log('\n3️⃣ Testing settings count...');
    const countResult = await executeQuery('SELECT COUNT(*) as count FROM system_settings');
    console.log('✅ Settings count successful:', countResult.rows[0]);
    
    // Test 4: Try the exact query that's failing
    console.log('\n4️⃣ Testing specific settings query...');
    const settingsResult = await executeQuery(
      'SELECT setting_value, data_type FROM system_settings WHERE category = $1 AND setting_key = $2',
      ['payment', 'min_order_amount']
    );
    console.log('✅ Specific query successful:', settingsResult.rows);
    
    // Test 5: Try getting all payment settings
    console.log('\n5️⃣ Testing payment settings query...');
    const paymentResult = await executeQuery(
      'SELECT setting_key, setting_value, data_type FROM system_settings WHERE category = $1',
      ['payment']
    );
    console.log('✅ Payment settings query successful:', paymentResult.rows);
    
    console.log('\n🎉 All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
  }
};

// Run the test
testDatabase().catch(console.error);
