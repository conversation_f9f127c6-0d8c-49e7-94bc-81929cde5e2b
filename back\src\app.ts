import express from 'express';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import { logger, morganStream, logErrorWithRequest } from './config/logger';
import { settingsService } from './services/settingsService';
import {
  requestContext,
  performanceMonitoring,
  errorContext,
  requestSizeMonitoring,
  userActivityLogging
} from './middleware/requestContext';
import {
  corsOptions,
  helmetConfig,
  generalRateLimit,
  speedLimiter,
  securityHeaders,
  detectSuspiciousActivity,
  checkBlockedIP,
  honeypot,
  requestSizeLimit,
  userGeneralRateLimit,
} from './middleware/security';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/user';
import orderRoutes from './routes/orders';
import paymentRoutes from './routes/payments';
import voucherRoutes from './routes/vouchers';
import adminRoutes from './routes/admin';
import tonRoutes from './routes/ton';
import webhookRoutes from './routes/webhooks';
import shopRoutes from './routes/shop';
import profileRoutes from './routes/profile';
import settingsRoutes from './routes/settings';
import publicSettingsRoutes from './routes/publicSettings';
import realtimeSettingsRoutes from './routes/realtimeSettings';

const app = express();

// Trust proxy (important for rate limiting and IP detection)
app.set('trust proxy', 1);

// Dynamic compression middleware based on settings
const setupCompression = async () => {
  try {
    const performanceSettings = await settingsService.getPerformanceSettings();
    if (performanceSettings.enableCompression) {
      app.use(compression());
      logger.info('Compression enabled via settings');
    } else {
      logger.info('Compression disabled via settings');
    }
  } catch (error) {
    logger.error('Failed to get compression setting, enabling by default:', error);
    app.use(compression());
  }
};

// Initialize compression (will be called after database connection)
let compressionInitialized = false;

// Request context and monitoring middleware (should be early in the chain)
app.use(requestContext);
app.use(performanceMonitoring(2000)); // Log requests taking more than 2 seconds
app.use(errorContext);
app.use(requestSizeMonitoring(5 * 1024 * 1024)); // Log requests larger than 5MB

// Basic middleware
app.use(cookieParser());

// Security middleware (order is important)
app.use(helmetConfig);
app.use(cors(corsOptions));
app.use(securityHeaders);
app.use(checkBlockedIP);
app.use(detectSuspiciousActivity);
app.use(requestSizeLimit(10 * 1024 * 1024)); // 10MB limit

// Rate limiting
app.use(generalRateLimit); // IP-based rate limiting
app.use(userGeneralRateLimit); // Per-user rate limiting (applied after authentication)
app.use(speedLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// User activity logging (after authentication middleware)
app.use(userActivityLogging);

// Honeypot middleware
app.use(honeypot);

// HTTP request logging
if (process.env.NODE_ENV !== 'test') {
  const morgan = require('morgan');
  app.use(morgan('combined', { stream: morganStream }));
}

// Health check endpoint (before authentication)
app.get('/health', (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API routes
app.use('/api/v1', (_req, res, next) => {
  // Add API version header
  res.setHeader('API-Version', '1.0.0');
  next();
});

// Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/user', userRoutes);
app.use('/api/v1/orders', orderRoutes);
app.use('/api/v1/payments', paymentRoutes);
app.use('/api/v1/vouchers', voucherRoutes);
app.use('/api/v1/admin', adminRoutes);
app.use('/api/v1/ton', tonRoutes);
app.use('/api/v1/webhooks', webhookRoutes);
app.use('/api/v1/shop', shopRoutes);
app.use('/api/v1/profile', profileRoutes);
app.use('/api/v1/settings', settingsRoutes);
app.use('/api/v1/public', publicSettingsRoutes);
app.use('/api/v1/auth/settings', realtimeSettingsRoutes);

// Temporary test endpoint
app.get('/api/v1/test', (_req, res) => {
  res.json({
    success: true,
    message: 'API is working',
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use('*', (req, res) => {
  logger.warn(`404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    error: 'Route not found',
  });
});

// Enhanced global error handler with comprehensive logging
app.use((error: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  // Log error with full request context
  const errorId = logErrorWithRequest(error, req, {
    statusCode: error.statusCode || 500,
    errorType: error.name || 'UnhandledError',
    requestBody: req.body,
    requestParams: req.params,
    requestQuery: req.query,
  });

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  const responseData: any = {
    success: false,
    error: isDevelopment ? error.message : 'Internal server error',
    errorId, // Always include error ID for tracking
  };

  // Include stack trace only in development
  if (isDevelopment && error.stack) {
    responseData.stack = error.stack;
  }

  res.status(error.statusCode || 500).json(responseData);
});

// Graceful shutdown handler
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}, shutting down gracefully...`);
  
  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Promise Rejection:', { reason, promise });
});

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Function to initialize dynamic settings after database connection
export const initializeDynamicSettings = async () => {
  if (!compressionInitialized) {
    await setupCompression();
    compressionInitialized = true;
  }
};

export default app;
