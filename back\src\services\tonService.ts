import { TonClient, WalletContractV4, internal } from '@ton/ton';
import { mnemonicNew, mnemonicToPrivateKey } from '@ton/crypto';
import { Address, Cell, beginCell, toNano } from '@ton/core';
import {
  logger,
  logError,
  logErrorWithRequest,
  logBusinessError,
  logServiceOperation,
  logExternalApiCall,
  logCriticalError
} from '../config/logger';
import { executeQuery } from '../config/database';
import { getBlockchainSettings } from './settingsService';
import { encryptPrivateKey, decryptPrivateKey } from '../utils/encryption';

// TON network configuration - now dynamic from settings with fallback to env
let tonNetworkConfig: {
  network: string;
  endpoint: string;
  confirmationBlocks: number;
  gasLimit: string;
} | null = null;

// Initialize TON client
let tonClient: TonClient;

/**
 * Get TON network configuration from settings with fallback to environment variables
 */
async function getTonNetworkConfig() {
  if (!tonNetworkConfig) {
    await refreshTonNetworkConfig();
  }
  return tonNetworkConfig!;
}

/**
 * Refresh TON network configuration from settings
 * This should be called when settings are updated
 */
export const refreshTonNetworkConfig = async () => {
  try {
    const blockchainSettings = await getBlockchainSettings();
    tonNetworkConfig = {
      network: blockchainSettings.tonNetwork,
      endpoint: blockchainSettings.tonApiEndpoint,
      confirmationBlocks: blockchainSettings.confirmationBlocks,
      gasLimit: blockchainSettings.gasLimit,
    };
    logger.info('TON network configuration refreshed from settings:', tonNetworkConfig);

    // If client is already initialized and endpoint changed, reinitialize
    if (tonClient) {
      logger.info('Reinitializing TON client due to configuration change');
      await initializeTonClient();
    }
  } catch (error) {
    logger.warn('Failed to load TON settings from database, falling back to environment variables:', error);
    // Fallback to environment variables
    const network = process.env.TON_NETWORK || 'testnet';
    tonNetworkConfig = {
      network,
      endpoint: process.env.TON_API_ENDPOINT ||
        (network === 'mainnet' ? 'https://toncenter.com/api/v2/jsonRPC' : 'https://testnet.toncenter.com/api/v2/jsonRPC'),
      confirmationBlocks: parseInt(process.env.TON_CONFIRMATION_BLOCKS || '3'),
      gasLimit: process.env.TON_GAS_LIMIT || '1000000',
    };
    logger.debug('TON network configuration loaded from environment:', tonNetworkConfig);
  }
};

export const initializeTonClient = async () => {
  const startTime = Date.now();

  try {
    const config = await getTonNetworkConfig();

    const clientConfig: any = {
      endpoint: config.endpoint,
    };

    // Add API key only if it exists
    if (process.env.TON_API_KEY) {
      clientConfig.apiKey = process.env.TON_API_KEY;
    }

    tonClient = new TonClient(clientConfig);
    const duration = Date.now() - startTime;

    logServiceOperation(
      'TON_SERVICE',
      'INITIALIZE_CLIENT',
      duration,
      true,
      {
        network: config.network,
        endpoint: config.endpoint,
        hasApiKey: !!process.env.TON_API_KEY
      }
    );

    logger.info('TON client initialized', {
      network: config.network,
      endpoint: config.endpoint,
      confirmationBlocks: config.confirmationBlocks,
      gasLimit: config.gasLimit,
      duration
    });

    return tonClient;
  } catch (error) {
    const duration = Date.now() - startTime;

    const errorId = logBusinessError(
      'TON_CLIENT_INITIALIZATION',
      error as Error,
      { duration, endpoint: process.env.TON_API_ENDPOINT }
    );

    logServiceOperation(
      'TON_SERVICE',
      'INITIALIZE_CLIENT',
      duration,
      false,
      { errorId }
    );

    throw error;
  }
};

// Get TON client instance
export const getTonClient = (): TonClient => {
  if (!tonClient) {
    throw new Error('TON client not initialized. Call initializeTonClient() first.');
  }
  return tonClient;
};

// Get current TON network configuration
export const getCurrentTonConfig = async () => {
  return await getTonNetworkConfig();
};

// Generate a new payment address for an order
export const generatePaymentAddress = async (orderId: string): Promise<{
  address: string;
  memo: string;
  privateKey?: string;
}> => {
  const startTime = Date.now();

  try {
    // For production, you might want to use a deterministic address generation
    // based on order ID or use a master wallet with different memo fields

    // Generate new mnemonic and keys
    const mnemonic = await mnemonicNew();
    const keyPair = await mnemonicToPrivateKey(mnemonic);

    // Create wallet contract
    const workchain = 0; // Usually 0 for basic wallets
    const wallet = WalletContractV4.create({ workchain, publicKey: keyPair.publicKey });

    // Get wallet address
    const address = wallet.address.toString();

    // Create memo for this order
    const memo = `Order:${orderId}`;

    // Encrypt private key before storing
    const privateKeyHex = keyPair.secretKey.toString('hex');
    const encryptedPrivateKey = encryptPrivateKey(privateKeyHex);

    // Store wallet info securely with encrypted private key
    await executeQuery(
      `INSERT INTO payment_addresses (order_id, address, private_key_encrypted, expires_at, created_at)
       VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)`,
      [orderId, address, encryptedPrivateKey, new Date(Date.now() + 24 * 60 * 60 * 1000)], // 24 hours expiry
      { operation: 'GENERATE_PAYMENT_ADDRESS', orderId }
    );

    const duration = Date.now() - startTime;

    logServiceOperation(
      'TON_SERVICE',
      'GENERATE_PAYMENT_ADDRESS',
      duration,
      true,
      { orderId, addressGenerated: true }
    );

    logger.info('Payment address generated', {
      orderId,
      address,
      memo,
      duration,
    });

    const result: { address: string; memo: string; privateKey?: string } = {
      address,
      memo,
    };

    // Don't return private key in production
    if (process.env.NODE_ENV === 'development') {
      result.privateKey = keyPair.secretKey.toString('hex');
    }

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    const errorId = logBusinessError(
      'GENERATE_PAYMENT_ADDRESS',
      error as Error,
      { orderId, duration }
    );

    logServiceOperation(
      'TON_SERVICE',
      'GENERATE_PAYMENT_ADDRESS',
      duration,
      false,
      { orderId, errorId }
    );

    throw error;
  }
};

// Verify a transaction on TON blockchain
export const verifyTransaction = async (
  transactionHash: string,
  expectedAmount: string,
  expectedDestination: string,
  memo?: string
): Promise<{
  isValid: boolean;
  transaction?: any;
  error?: string;
}> => {
  try {
    const client = getTonClient();
    
    // Get transaction by hash
    const transactions = await client.getTransactions(
      Address.parse(expectedDestination),
      { limit: 100 }
    );
    
    // Find transaction with matching hash
    const transaction = transactions.find(tx => 
      tx.hash().toString('hex') === transactionHash.toLowerCase()
    );
    
    if (!transaction) {
      return {
        isValid: false,
        error: 'Transaction not found',
      };
    }
    
    // Verify transaction details
    const inMsg = transaction.inMessage;
    if (!inMsg) {
      return {
        isValid: false,
        error: 'No incoming message in transaction',
      };
    }
    
    // Check if it's an internal message (TON transfer)
    if (inMsg.info.type !== 'internal') {
      return {
        isValid: false,
        error: 'Not an internal transfer',
      };
    }
    
    // Verify amount
    const receivedAmount = inMsg.info.value.coins;
    const expectedAmountNano = toNano(expectedAmount);
    
    if (receivedAmount < expectedAmountNano) {
      return {
        isValid: false,
        error: `Insufficient amount. Expected: ${expectedAmount} TON, Received: ${receivedAmount.toString()} nanoTON`,
      };
    }
    
    // Verify destination address
    const destinationAddress = inMsg.info.dest?.toString();
    if (destinationAddress !== expectedDestination) {
      return {
        isValid: false,
        error: 'Destination address mismatch',
      };
    }
    
    // Verify memo if provided
    if (memo && inMsg.body) {
      try {
        const bodySlice = inMsg.body.beginParse();
        const op = bodySlice.loadUint(32);
        
        if (op === 0) { // Text comment
          const comment = bodySlice.loadStringTail();
          if (!comment.includes(memo)) {
            return {
              isValid: false,
              error: 'Memo mismatch',
            };
          }
        }
      } catch (error) {
        // Memo verification failed, but transaction might still be valid
        logger.warn('Memo verification failed:', error);
      }
    }
    
    logger.info('Transaction verified successfully', {
      transactionHash,
      amount: receivedAmount.toString(),
      destination: destinationAddress,
    });
    
    return {
      isValid: true,
      transaction: {
        hash: transactionHash,
        amount: receivedAmount.toString(),
        destination: destinationAddress,
        timestamp: transaction.now,
        lt: transaction.lt.toString(),
      },
    };
  } catch (error) {
    logger.error('Transaction verification failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      isValid: false,
      error: `Verification error: ${errorMessage}`,
    };
  }
};

// Get transaction status
export const getTransactionStatus = async (
  transactionHash: string
): Promise<{
  exists: boolean;
  confirmed: boolean;
  confirmations?: number;
  transaction?: any;
}> => {
  try {
    const client = getTonClient();
    const config = await getTonNetworkConfig();

    // In TON, we need to search for the transaction across known addresses
    // This is a simplified version - in production, you'd track specific addresses

    // For now, return a basic status check
    // In a real implementation, you'd need to track the specific address
    // and check transactions for that address

    return {
      exists: true,
      confirmed: true,
      confirmations: config.confirmationBlocks, // Use dynamic confirmation blocks
    };
  } catch (error) {
    logger.error('Failed to get transaction status:', error);
    return {
      exists: false,
      confirmed: false,
    };
  }
};

// Get current TON price (for display purposes)
export const getTonPrice = async (): Promise<number> => {
  try {
    // This would typically call a price API
    // For now, return a mock price
    return 2.50; // USD per TON
  } catch (error) {
    logger.error('Failed to get TON price:', error);
    return 0;
  }
};

// Convert TON amount to nanoTON
export const tonToNano = (amount: string | number): bigint => {
  return toNano(amount.toString());
};

// Convert nanoTON to TON
export const nanoToTon = (nanoAmount: bigint | string): string => {
  const nano = typeof nanoAmount === 'string' ? BigInt(nanoAmount) : nanoAmount;
  return (Number(nano) / 1000000000).toFixed(9);
};

// Validate TON address format
export const isValidTonAddress = (address: string): boolean => {
  try {
    Address.parse(address);
    return true;
  } catch {
    return false;
  }
};

// Get wallet balance
export const getWalletBalance = async (address: string): Promise<string> => {
  try {
    const client = getTonClient();
    const balance = await client.getBalance(Address.parse(address));
    return nanoToTon(balance);
  } catch (error) {
    logger.error('Failed to get wallet balance:', error);
    return '0';
  }
};

/**
 * Retrieve and decrypt private key for a payment address
 * This function should only be used when a transaction needs to be sent
 */
export const getDecryptedPrivateKey = async (addressOrOrderId: string): Promise<string> => {
  try {
    let query: string;
    let params: string[];

    // Check if it's an order ID (UUID format) or an address
    const isOrderId = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(addressOrOrderId);

    if (isOrderId) {
      query = 'SELECT private_key_encrypted FROM payment_addresses WHERE order_id = $1 AND private_key_encrypted IS NOT NULL';
      params = [addressOrOrderId];
    } else {
      query = 'SELECT private_key_encrypted FROM payment_addresses WHERE address = $1 AND private_key_encrypted IS NOT NULL';
      params = [addressOrOrderId];
    }

    const result = await executeQuery(query, params);

    if (result.rows.length === 0) {
      throw new Error('Payment address not found or private key not available');
    }

    const encryptedPrivateKey = result.rows[0].private_key_encrypted;
    if (!encryptedPrivateKey) {
      throw new Error('Private key not found for this address');
    }

    // Decrypt the private key
    const privateKey = decryptPrivateKey(encryptedPrivateKey);

    logger.debug('Private key retrieved and decrypted', {
      addressOrOrderId: isOrderId ? 'order-id' : 'address',
      keyLength: privateKey.length
    });

    return privateKey;
  } catch (error) {
    logger.error('Failed to retrieve private key:', error);
    throw new Error('Failed to retrieve private key for transaction');
  }
};

/**
 * Send TON transaction using encrypted private key from database
 * This is the secure version that retrieves and decrypts private keys
 */
export const sendTransactionFromAddress = async (
  fromAddressOrOrderId: string,
  toAddress: string,
  amount: string,
  memo?: string
): Promise<{
  success: boolean;
  transactionHash?: string;
  error?: string;
}> => {
  try {
    // Retrieve and decrypt private key
    const privateKey = await getDecryptedPrivateKey(fromAddressOrOrderId);

    // Use the internal sendTransaction function
    return await sendTransactionInternal(privateKey, toAddress, amount, memo);
  } catch (error) {
    logger.error('Failed to send transaction from address:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Internal function to send TON transaction with raw private key
 * This should not be used directly - use sendTransactionFromAddress instead
 */
const sendTransactionInternal = async (
  fromPrivateKey: string,
  toAddress: string,
  amount: string,
  memo?: string
): Promise<{
  success: boolean;
  transactionHash?: string;
  error?: string;
}> => {
  try {
    const client = getTonClient();

    // This is a simplified version - in production you'd need proper key management
    const privateKey = Buffer.from(fromPrivateKey, 'hex');

    // Create wallet from private key
    const keyPair = {
      publicKey: privateKey.slice(32), // Last 32 bytes
      secretKey: privateKey,
    };

    const wallet = WalletContractV4.create({
      workchain: 0,
      publicKey: keyPair.publicKey
    });

    const contract = client.open(wallet);

    // Create transfer message
    let body: Cell | undefined;
    if (memo) {
      body = beginCell()
        .storeUint(0, 32) // Text comment op code
        .storeStringTail(memo)
        .endCell();
    }

    // Send transaction
    const seqno = await contract.getSeqno();

    await contract.sendTransfer({
      secretKey: keyPair.secretKey,
      seqno,
      messages: [
        internal({
          to: Address.parse(toAddress),
          value: tonToNano(amount),
          body,
        }),
      ],
    });

    // Wait for transaction to be processed
    let currentSeqno = seqno;
    while (currentSeqno === seqno) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      currentSeqno = await contract.getSeqno();
    }

    logger.info('Transaction sent successfully', {
      toAddress,
      amount,
      memo,
    });

    return {
      success: true,
      transactionHash: 'pending', // Would need to get actual hash
    };
  } catch (error) {
    logger.error('Failed to send transaction:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Send TON transaction (for admin operations)
 * @deprecated Use sendTransactionFromAddress for better security
 */
export const sendTransaction = sendTransactionInternal;

// Health check for TON service
export const checkTonServiceHealth = async (): Promise<{
  healthy: boolean;
  network: string;
  endpoint: string;
  error?: string;
}> => {
  try {
    const client = getTonClient();
    const config = await getTonNetworkConfig();

    // Try to get master chain info to verify connection
    const masterchainInfo = await client.getMasterchainInfo();

    return {
      healthy: true,
      network: config.network,
      endpoint: config.endpoint,
    };
  } catch (error) {
    logger.error('TON service health check failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    try {
      const config = await getTonNetworkConfig();
      return {
        healthy: false,
        network: config.network,
        endpoint: config.endpoint,
        error: errorMessage,
      };
    } catch (configError) {
      // Fallback if even config loading fails
      return {
        healthy: false,
        network: 'unknown',
        endpoint: 'unknown',
        error: errorMessage,
      };
    }
  }
};
