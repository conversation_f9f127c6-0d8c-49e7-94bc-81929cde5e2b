/**
 * Encryption utilities for secure data storage
 * 
 * This module provides AES-256-GCM encryption for sensitive data like private keys.
 * Uses Node.js built-in crypto module for maximum security and performance.
 */

import crypto from 'crypto';
import { logger } from '../config/logger';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For GCM, this is 12-16 bytes
const TAG_LENGTH = 16; // GCM authentication tag length
const SALT_LENGTH = 32; // Salt for key derivation

/**
 * Get encryption key from environment variable
 * In production, this should be managed by a key management service
 */
function getEncryptionKey(): string {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is required');
  }
  if (key.length < 32) {
    throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
  }
  return key;
}

/**
 * Derive a key from the master key using PBKDF2
 * This adds an extra layer of security and allows for key rotation
 */
function deriveKey(masterKey: string, salt: Buffer): Buffer {
  return crypto.pbkdf2Sync(masterKey, salt, 100000, 32, 'sha256');
}

/**
 * Encrypt sensitive data using AES-256-GCM
 * 
 * @param plaintext - The data to encrypt
 * @returns Encrypted data as base64 string with format: salt:iv:tag:ciphertext
 */
export function encryptSensitiveData(plaintext: string): string {
  try {
    if (!plaintext) {
      throw new Error('Plaintext cannot be empty');
    }

    const masterKey = getEncryptionKey();
    
    // Generate random salt and IV
    const salt = crypto.randomBytes(SALT_LENGTH);
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Derive encryption key
    const key = deriveKey(masterKey, salt);
    
    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    cipher.setAAD(Buffer.from('payment-key')); // Additional authenticated data
    
    // Encrypt the data
    let ciphertext = cipher.update(plaintext, 'utf8', 'base64');
    ciphertext += cipher.final('base64');
    
    // Get authentication tag
    const tag = cipher.getAuthTag();
    
    // Combine all components: salt:iv:tag:ciphertext
    const result = [
      salt.toString('base64'),
      iv.toString('base64'),
      tag.toString('base64'),
      ciphertext
    ].join(':');
    
    logger.debug('Data encrypted successfully', {
      saltLength: salt.length,
      ivLength: iv.length,
      tagLength: tag.length,
      ciphertextLength: ciphertext.length
    });
    
    return result;
  } catch (error) {
    logger.error('Encryption failed:', error);
    throw new Error('Failed to encrypt sensitive data');
  }
}

/**
 * Decrypt sensitive data using AES-256-GCM
 * 
 * @param encryptedData - Encrypted data in format: salt:iv:tag:ciphertext
 * @returns Decrypted plaintext
 */
export function decryptSensitiveData(encryptedData: string): string {
  try {
    if (!encryptedData) {
      throw new Error('Encrypted data cannot be empty');
    }

    const masterKey = getEncryptionKey();
    
    // Parse the encrypted data
    const parts = encryptedData.split(':');
    if (parts.length !== 4) {
      throw new Error('Invalid encrypted data format');
    }
    
    const [saltB64, ivB64, tagB64, ciphertext] = parts;
    
    // Convert from base64
    const salt = Buffer.from(saltB64, 'base64');
    const iv = Buffer.from(ivB64, 'base64');
    const tag = Buffer.from(tagB64, 'base64');
    
    // Validate lengths
    if (salt.length !== SALT_LENGTH) {
      throw new Error('Invalid salt length');
    }
    if (iv.length !== IV_LENGTH) {
      throw new Error('Invalid IV length');
    }
    if (tag.length !== TAG_LENGTH) {
      throw new Error('Invalid tag length');
    }
    
    // Derive decryption key
    const key = deriveKey(masterKey, salt);
    
    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    decipher.setAAD(Buffer.from('payment-key')); // Same AAD as encryption
    
    // Decrypt the data
    let plaintext = decipher.update(ciphertext, 'base64', 'utf8');
    plaintext += decipher.final('utf8');
    
    logger.debug('Data decrypted successfully');
    
    return plaintext;
  } catch (error) {
    logger.error('Decryption failed:', error);
    throw new Error('Failed to decrypt sensitive data');
  }
}

/**
 * Encrypt a private key specifically
 * This is a convenience wrapper for private key encryption
 */
export function encryptPrivateKey(privateKey: string): string {
  if (!privateKey) {
    throw new Error('Private key cannot be empty');
  }
  
  // Validate private key format (hex string)
  if (!/^[a-fA-F0-9]+$/.test(privateKey)) {
    throw new Error('Private key must be a valid hex string');
  }
  
  logger.debug('Encrypting private key', {
    keyLength: privateKey.length
  });
  
  return encryptSensitiveData(privateKey);
}

/**
 * Decrypt a private key specifically
 * This is a convenience wrapper for private key decryption
 */
export function decryptPrivateKey(encryptedPrivateKey: string): string {
  if (!encryptedPrivateKey) {
    throw new Error('Encrypted private key cannot be empty');
  }
  
  logger.debug('Decrypting private key');
  
  const privateKey = decryptSensitiveData(encryptedPrivateKey);
  
  // Validate decrypted private key format
  if (!/^[a-fA-F0-9]+$/.test(privateKey)) {
    throw new Error('Decrypted private key is not a valid hex string');
  }
  
  return privateKey;
}

/**
 * Test encryption/decryption functionality
 * This function can be used to verify the encryption is working correctly
 */
export function testEncryption(): boolean {
  try {
    const testData = 'test-private-key-' + crypto.randomBytes(16).toString('hex');
    const encrypted = encryptSensitiveData(testData);
    const decrypted = decryptSensitiveData(encrypted);
    
    const success = testData === decrypted;
    
    if (success) {
      logger.info('Encryption test passed');
    } else {
      logger.error('Encryption test failed: data mismatch');
    }
    
    return success;
  } catch (error) {
    logger.error('Encryption test failed:', error);
    return false;
  }
}

/**
 * Validate encryption key strength
 * Checks if the encryption key meets security requirements
 */
export function validateEncryptionKey(): boolean {
  try {
    const key = process.env.ENCRYPTION_KEY;
    
    if (!key) {
      logger.error('ENCRYPTION_KEY environment variable is not set');
      return false;
    }
    
    if (key.length < 32) {
      logger.error('ENCRYPTION_KEY is too short (minimum 32 characters)');
      return false;
    }
    
    if (key.length < 64) {
      logger.warn('ENCRYPTION_KEY should be at least 64 characters for optimal security');
    }
    
    // Check for common weak patterns
    if (key === key.toLowerCase() || key === key.toUpperCase()) {
      logger.warn('ENCRYPTION_KEY should contain mixed case characters');
    }
    
    if (!/\d/.test(key)) {
      logger.warn('ENCRYPTION_KEY should contain numbers');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(key)) {
      logger.warn('ENCRYPTION_KEY should contain special characters');
    }
    
    logger.info('Encryption key validation passed');
    return true;
  } catch (error) {
    logger.error('Encryption key validation failed:', error);
    return false;
  }
}
