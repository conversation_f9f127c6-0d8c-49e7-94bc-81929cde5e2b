/**
 * Simple test for encryption implementation
 * Tests the core encryption functionality without TypeScript dependencies
 */

const crypto = require('crypto');

// Set test environment variables
process.env.ENCRYPTION_KEY = 'test-encryption-key-' + crypto.randomBytes(32).toString('hex');

// Encryption configuration (same as in encryption.ts)
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

/**
 * Get encryption key from environment variable
 */
function getEncryptionKey() {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is required');
  }
  if (key.length < 32) {
    throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
  }
  return key;
}

/**
 * Derive a key from the master key using PBKDF2
 */
function deriveKey(masterKey, salt) {
  return crypto.pbkdf2Sync(masterK<PERSON>, salt, 100000, 32, 'sha256');
}

/**
 * Encrypt sensitive data using AES-256-GCM
 */
function encryptSensitiveData(plaintext) {
  if (!plaintext) {
    throw new Error('Plaintext cannot be empty');
  }

  const masterKey = getEncryptionKey();
  
  // Generate random salt and IV
  const salt = crypto.randomBytes(SALT_LENGTH);
  const iv = crypto.randomBytes(IV_LENGTH);
  
  // Derive encryption key
  const key = deriveKey(masterKey, salt);
  
  // Create cipher
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
  cipher.setAAD(Buffer.from('payment-key'));
  
  // Encrypt the data
  let ciphertext = cipher.update(plaintext, 'utf8', 'base64');
  ciphertext += cipher.final('base64');
  
  // Get authentication tag
  const tag = cipher.getAuthTag();
  
  // Combine all components: salt:iv:tag:ciphertext
  const result = [
    salt.toString('base64'),
    iv.toString('base64'),
    tag.toString('base64'),
    ciphertext
  ].join(':');
  
  return result;
}

/**
 * Decrypt sensitive data using AES-256-GCM
 */
function decryptSensitiveData(encryptedData) {
  if (!encryptedData) {
    throw new Error('Encrypted data cannot be empty');
  }

  const masterKey = getEncryptionKey();
  
  // Parse the encrypted data
  const parts = encryptedData.split(':');
  if (parts.length !== 4) {
    throw new Error('Invalid encrypted data format');
  }
  
  const [saltB64, ivB64, tagB64, ciphertext] = parts;
  
  // Convert from base64
  const salt = Buffer.from(saltB64, 'base64');
  const iv = Buffer.from(ivB64, 'base64');
  const tag = Buffer.from(tagB64, 'base64');
  
  // Validate lengths
  if (salt.length !== SALT_LENGTH) {
    throw new Error('Invalid salt length');
  }
  if (iv.length !== IV_LENGTH) {
    throw new Error('Invalid IV length');
  }
  if (tag.length !== TAG_LENGTH) {
    throw new Error('Invalid tag length');
  }
  
  // Derive decryption key
  const key = deriveKey(masterKey, salt);
  
  // Create decipher
  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(tag);
  decipher.setAAD(Buffer.from('payment-key'));
  
  // Decrypt the data
  let plaintext = decipher.update(ciphertext, 'base64', 'utf8');
  plaintext += decipher.final('utf8');
  
  return plaintext;
}

/**
 * Encrypt a private key specifically
 */
function encryptPrivateKey(privateKey) {
  if (!privateKey) {
    throw new Error('Private key cannot be empty');
  }
  
  // Validate private key format (hex string)
  if (!/^[a-fA-F0-9]+$/.test(privateKey)) {
    throw new Error('Private key must be a valid hex string');
  }
  
  return encryptSensitiveData(privateKey);
}

/**
 * Decrypt a private key specifically
 */
function decryptPrivateKey(encryptedPrivateKey) {
  if (!encryptedPrivateKey) {
    throw new Error('Encrypted private key cannot be empty');
  }
  
  const privateKey = decryptSensitiveData(encryptedPrivateKey);
  
  // Validate decrypted private key format
  if (!/^[a-fA-F0-9]+$/.test(privateKey)) {
    throw new Error('Decrypted private key is not a valid hex string');
  }
  
  return privateKey;
}

function runEncryptionTests() {
  console.log('🔐 Testing Private Key Encryption Implementation\n');

  let testsPassed = 0;
  let totalTests = 0;

  function test(name, testFn) {
    totalTests++;
    try {
      console.log(`${totalTests}. ${name}...`);
      testFn();
      console.log('✅ PASS\n');
      testsPassed++;
    } catch (error) {
      console.log(`❌ FAIL: ${error.message}\n`);
    }
  }

  // Test 1: Basic encryption/decryption
  test('Basic encryption and decryption', () => {
    const testData = 'test-data-' + crypto.randomBytes(16).toString('hex');
    const encrypted = encryptSensitiveData(testData);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (testData !== decrypted) {
      throw new Error('Decrypted data does not match original');
    }
    
    console.log(`   Original: ${testData.substring(0, 20)}...`);
    console.log(`   Encrypted: ${encrypted.substring(0, 40)}...`);
    console.log(`   Decrypted: ${decrypted.substring(0, 20)}...`);
  });

  // Test 2: Private key encryption
  test('Private key encryption', () => {
    const privateKey = crypto.randomBytes(32).toString('hex');
    const encrypted = encryptPrivateKey(privateKey);
    const decrypted = decryptPrivateKey(encrypted);
    
    if (privateKey !== decrypted) {
      throw new Error('Decrypted private key does not match original');
    }
    
    console.log(`   Private key length: ${privateKey.length}`);
    console.log(`   Encrypted format: ${encrypted.split(':').length} parts`);
    console.log(`   Decryption successful: ${decrypted.length} chars`);
  });

  // Test 3: Multiple encryptions produce different results
  test('Multiple encryptions produce different ciphertexts', () => {
    const privateKey = crypto.randomBytes(32).toString('hex');
    const encrypted1 = encryptPrivateKey(privateKey);
    const encrypted2 = encryptPrivateKey(privateKey);
    
    if (encrypted1 === encrypted2) {
      throw new Error('Multiple encryptions should produce different ciphertexts');
    }
    
    // But both should decrypt to the same value
    const decrypted1 = decryptPrivateKey(encrypted1);
    const decrypted2 = decryptPrivateKey(encrypted2);
    
    if (decrypted1 !== privateKey || decrypted2 !== privateKey) {
      throw new Error('Both encryptions should decrypt to original value');
    }
    
    console.log('   ✓ Different ciphertexts for same input (good!)');
    console.log('   ✓ Both decrypt to original value');
  });

  // Test 4: Invalid input handling
  test('Error handling for invalid inputs', () => {
    let errorCount = 0;
    
    // Test empty private key
    try {
      encryptPrivateKey('');
    } catch (error) {
      errorCount++;
      console.log('   ✓ Correctly rejected empty private key');
    }
    
    // Test invalid hex private key
    try {
      encryptPrivateKey('invalid-hex-key');
    } catch (error) {
      errorCount++;
      console.log('   ✓ Correctly rejected invalid hex private key');
    }
    
    // Test invalid encrypted data
    try {
      decryptPrivateKey('invalid-encrypted-data');
    } catch (error) {
      errorCount++;
      console.log('   ✓ Correctly rejected invalid encrypted data');
    }
    
    // Test corrupted encrypted data
    try {
      const validKey = crypto.randomBytes(32).toString('hex');
      const encrypted = encryptPrivateKey(validKey);
      const corrupted = encrypted.substring(0, encrypted.length - 5) + 'XXXXX';
      decryptPrivateKey(corrupted);
    } catch (error) {
      errorCount++;
      console.log('   ✓ Correctly rejected corrupted encrypted data');
    }
    
    if (errorCount !== 4) {
      throw new Error(`Expected 4 errors, got ${errorCount}`);
    }
  });

  // Test 5: Encryption format validation
  test('Encrypted data format validation', () => {
    const privateKey = crypto.randomBytes(32).toString('hex');
    const encrypted = encryptPrivateKey(privateKey);
    
    // Should have 4 parts separated by colons
    const parts = encrypted.split(':');
    if (parts.length !== 4) {
      throw new Error(`Expected 4 parts, got ${parts.length}`);
    }
    
    // Each part should be valid base64
    parts.forEach((part, index) => {
      try {
        Buffer.from(part, 'base64');
        console.log(`   ✓ Part ${index + 1} is valid base64`);
      } catch (error) {
        throw new Error(`Part ${index + 1} is not valid base64`);
      }
    });
    
    // Validate component lengths
    const salt = Buffer.from(parts[0], 'base64');
    const iv = Buffer.from(parts[1], 'base64');
    const tag = Buffer.from(parts[2], 'base64');
    
    if (salt.length !== SALT_LENGTH) {
      throw new Error(`Salt length should be ${SALT_LENGTH}, got ${salt.length}`);
    }
    if (iv.length !== IV_LENGTH) {
      throw new Error(`IV length should be ${IV_LENGTH}, got ${iv.length}`);
    }
    if (tag.length !== TAG_LENGTH) {
      throw new Error(`Tag length should be ${TAG_LENGTH}, got ${tag.length}`);
    }
    
    console.log(`   ✓ Salt: ${salt.length} bytes`);
    console.log(`   ✓ IV: ${iv.length} bytes`);
    console.log(`   ✓ Tag: ${tag.length} bytes`);
    console.log(`   ✓ Ciphertext: ${parts[3].length} chars`);
  });

  // Test 6: Performance test
  test('Performance test (100 encryptions)', () => {
    const startTime = Date.now();
    const privateKey = crypto.randomBytes(32).toString('hex');
    
    for (let i = 0; i < 100; i++) {
      const encrypted = encryptPrivateKey(privateKey);
      const decrypted = decryptPrivateKey(encrypted);
      
      if (decrypted !== privateKey) {
        throw new Error(`Encryption/decryption failed at iteration ${i}`);
      }
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    const avgTime = duration / 100;
    
    console.log(`   ✓ 100 encrypt/decrypt cycles completed`);
    console.log(`   ✓ Total time: ${duration}ms`);
    console.log(`   ✓ Average time per cycle: ${avgTime.toFixed(2)}ms`);
    
    if (avgTime > 100) {
      throw new Error('Performance too slow (>100ms per cycle)');
    }
  });

  // Summary
  console.log('📊 Test Results Summary:');
  console.log('=' .repeat(50));
  console.log(`Tests passed: ${testsPassed}/${totalTests}`);
  console.log(`Success rate: ${((testsPassed / totalTests) * 100).toFixed(1)}%`);
  
  if (testsPassed === totalTests) {
    console.log('\n🎉 All tests passed! Private key encryption is working correctly.');
    console.log('\nImplementation features:');
    console.log('✅ AES-256-GCM encryption');
    console.log('✅ PBKDF2 key derivation');
    console.log('✅ Random salt and IV for each encryption');
    console.log('✅ Authentication tag for integrity');
    console.log('✅ Proper error handling');
    console.log('✅ Good performance');
    return true;
  } else {
    console.log('\n❌ Some tests failed. Please fix the issues.');
    return false;
  }
}

// Run tests if called directly
if (require.main === module) {
  const success = runEncryptionTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runEncryptionTests };
