'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  <PERSON>,
  Copy,
  Eye,
  EyeOff,
  Download,
  CheckCircle,
  Clock,
  AlertCircle,
  Search,
  Filter,
} from 'lucide-react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

interface Voucher {
  id: string;
  code: string;
  type: string;
  amount: string;
  currency: string;
  status: 'active' | 'redeemed' | 'expired' | 'pending';
  createdAt: string;
  expiresAt: string;
  orderId: string;
  instructions?: string;
}

export default function VouchersPage() {
  const { user } = useAuth();
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [visibleCodes, setVisibleCodes] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadVouchers();
  }, []);

  const loadVouchers = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setLoading(false);
        return;
      }

      const response = await fetch('/api/v1/vouchers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setVouchers(data.data.vouchers);
        }
      } else {
        console.error('Failed to load vouchers:', response.statusText);
      }
    } catch (error) {
      console.error('Failed to load vouchers:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-success-600" />;
      case 'redeemed':
        return <CheckCircle className="h-5 w-5 text-gray-600" />;
      case 'expired':
        return <AlertCircle className="h-5 w-5 text-error-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-warning-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-success-600 bg-success-100';
      case 'redeemed':
        return 'text-gray-600 bg-gray-100';
      case 'expired':
        return 'text-error-600 bg-error-100';
      case 'pending':
        return 'text-warning-600 bg-warning-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const toggleCodeVisibility = (voucherId: string) => {
    const newVisibleCodes = new Set(visibleCodes);
    if (newVisibleCodes.has(voucherId)) {
      newVisibleCodes.delete(voucherId);
    } else {
      newVisibleCodes.add(voucherId);
    }
    setVisibleCodes(newVisibleCodes);
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard!`);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const downloadVoucher = (voucher: Voucher) => {
    const content = `
Voucher Details
===============
Type: ${voucher.type}
Amount: ${voucher.amount} ${voucher.currency}
Code: ${voucher.code}
Status: ${voucher.status}
Created: ${formatDate(voucher.createdAt)}
Expires: ${formatDate(voucher.expiresAt)}
Order ID: ${voucher.orderId}

${voucher.instructions ? `Instructions:\n${voucher.instructions}` : ''}

Generated from TON Voucher Platform
    `.trim();

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `voucher-${voucher.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Voucher downloaded!');
  };

  const filteredVouchers = vouchers.filter(voucher => {
    const matchesFilter = filter === 'all' || voucher.status === filter;
    const matchesSearch = voucher.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voucher.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voucher.code.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="spinner-lg mx-auto mb-4" />
            <p className="text-gray-600">Loading vouchers...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Vouchers</h1>
            <p className="text-gray-600">Access and manage your purchased voucher codes</p>
          </div>
          <Link
            href="/dashboard/orders/new"
            className="btn-primary inline-flex items-center"
          >
            <Gift className="h-4 w-4 mr-2" />
            Buy Vouchers
          </Link>
        </div>

        {/* Filters and Search */}
        <div className="card">
          <div className="card-body">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search vouchers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input pl-10"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="input"
                >
                  <option value="all">All Vouchers</option>
                  <option value="active">Active</option>
                  <option value="redeemed">Redeemed</option>
                  <option value="pending">Pending</option>
                  <option value="expired">Expired</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Vouchers Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredVouchers.length > 0 ? (
            filteredVouchers.map((voucher) => (
              <div key={voucher.id} className="card">
                <div className="card-header">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Gift className="h-5 w-5 text-ton-600" />
                      <h3 className="font-medium text-gray-900">{voucher.type}</h3>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(voucher.status)}
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(voucher.status)}`}>
                        {voucher.status}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="card-body space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Amount:</span>
                      <p className="font-medium">{voucher.amount} {voucher.currency}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Expires:</span>
                      <p className="font-medium">{formatDate(voucher.expiresAt)}</p>
                    </div>
                  </div>

                  {/* Voucher Code */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Voucher Code:</span>
                      <button
                        onClick={() => toggleCodeVisibility(voucher.id)}
                        className="text-sm text-ton-600 hover:text-ton-500 inline-flex items-center"
                      >
                        {visibleCodes.has(voucher.id) ? (
                          <>
                            <EyeOff className="h-4 w-4 mr-1" />
                            Hide
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-1" />
                            Show
                          </>
                        )}
                      </button>
                    </div>
                    <div className="relative">
                      <input
                        type="text"
                        value={visibleCodes.has(voucher.id) ? voucher.code : '••••••••••••••••••••'}
                        readOnly
                        className="input font-mono text-sm pr-10"
                      />
                      {visibleCodes.has(voucher.id) && (
                        <button
                          onClick={() => copyToClipboard(voucher.code, 'Voucher code')}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
                        >
                          <Copy className="h-4 w-4 text-gray-400" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Instructions */}
                  {voucher.instructions && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>How to redeem:</strong> {voucher.instructions}
                      </p>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <button
                      onClick={() => downloadVoucher(voucher)}
                      className="btn-outline flex-1 inline-flex items-center justify-center text-sm"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </button>
                    <Link
                      href={`/dashboard/orders/${voucher.orderId}`}
                      className="btn-outline flex-1 inline-flex items-center justify-center text-sm"
                    >
                      View Order
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full">
              <div className="text-center py-12">
                <Gift className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No vouchers found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filter === 'all' 
                    ? "You haven't purchased any vouchers yet."
                    : `No ${filter} vouchers found.`}
                </p>
                <div className="mt-6">
                  <Link
                    href="/dashboard/orders/new"
                    className="btn-primary inline-flex items-center"
                  >
                    <Gift className="h-4 w-4 mr-2" />
                    Purchase your first voucher
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
