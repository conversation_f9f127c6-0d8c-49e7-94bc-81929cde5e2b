import { Router } from 'express';
import {
  getProducts,
  getProductById,
  getCategories,
  getFeaturedProducts,
  searchProducts,
} from '../controllers/shopController';
import { validateInput } from '../middleware/security';
import { query, param } from 'express-validator';

const router = Router();

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// Product filtering validation
const productFilterValidation = [
  query('category')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be between 1 and 50 characters'),
  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('sortBy')
    .optional()
    .isIn(['name', 'rating', 'min_amount', 'max_amount', 'sort_order', 'created_at'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  query('popular')
    .optional()
    .isBoolean()
    .withMessage('Popular must be a boolean'),
];

// Product ID validation
const productIdValidation = [
  param('id')
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Invalid product ID format'),
];

// Search validation
const searchValidation = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
];

/**
 * @route   GET /api/v1/shop/products
 * @desc    Get all products with filtering and pagination
 * @access  Public
 */
router.get(
  '/products',
  validateInput([...paginationValidation, ...productFilterValidation]),
  getProducts
);

/**
 * @route   GET /api/v1/shop/products/:id
 * @desc    Get product by ID
 * @access  Public
 */
router.get(
  '/products/:id',
  validateInput(productIdValidation),
  getProductById
);

/**
 * @route   GET /api/v1/shop/categories
 * @desc    Get all product categories
 * @access  Public
 */
router.get('/categories', getCategories);

/**
 * @route   GET /api/v1/shop/featured
 * @desc    Get featured/popular products
 * @access  Public
 */
router.get(
  '/featured',
  validateInput([
    query('limit')
      .optional()
      .isInt({ min: 1, max: 20 })
      .withMessage('Limit must be between 1 and 20'),
  ]),
  getFeaturedProducts
);

/**
 * @route   GET /api/v1/shop/search
 * @desc    Search products
 * @access  Public
 */
router.get(
  '/search',
  validateInput(searchValidation),
  searchProducts
);

export default router;
