import { Pool } from 'pg';

// Direct database configuration
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'tonsite_dev',
  user: 'postgres',
  password: '1234',
  ssl: false,
};

const flushDatabase = async () => {
  const db = new Pool(dbConfig);
  
  try {
    console.log('🗑️  Starting database flush...');

    // Test database connection
    await db.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Disable foreign key constraints temporarily
    console.log('🔓 Disabling foreign key constraints...');
    await db.query('SET session_replication_role = replica;');

    // Get all table names (excluding system tables)
    const tablesResult = await db.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY tablename;
    `);

    const tables = tablesResult.rows.map(row => row.tablename);
    console.log(`📋 Found ${tables.length} tables to flush:`, tables);

    // Truncate all tables
    for (const table of tables) {
      try {
        console.log(`🧹 Truncating table: ${table}`);
        await db.query(`TRUNCATE TABLE "${table}" RESTART IDENTITY CASCADE;`);
        console.log(`✅ Truncated: ${table}`);
      } catch (error: any) {
        console.error(`❌ Error truncating ${table}:`, error.message);
      }
    }

    // Re-enable foreign key constraints
    console.log('🔒 Re-enabling foreign key constraints...');
    await db.query('SET session_replication_role = DEFAULT;');

    // Reset all sequences to start from 1
    console.log('🔄 Resetting sequences...');
    const sequencesResult = await db.query(`
      SELECT sequence_name 
      FROM information_schema.sequences 
      WHERE sequence_schema = 'public';
    `);

    for (const seq of sequencesResult.rows) {
      try {
        await db.query(`ALTER SEQUENCE "${seq.sequence_name}" RESTART WITH 1;`);
        console.log(`✅ Reset sequence: ${seq.sequence_name}`);
      } catch (error: any) {
        console.error(`❌ Error resetting sequence ${seq.sequence_name}:`, error.message);
      }
    }

    // Verify all tables are empty
    console.log('🔍 Verifying database is empty...');
    let totalRows = 0;
    for (const table of tables) {
      try {
        const countResult = await db.query(`SELECT COUNT(*) as count FROM "${table}";`);
        const count = parseInt(countResult.rows[0].count);
        totalRows += count;
        if (count > 0) {
          console.log(`⚠️  Table ${table} still has ${count} rows`);
        }
      } catch (error: any) {
        console.error(`❌ Error checking ${table}:`, error.message);
      }
    }

    if (totalRows === 0) {
      console.log('✅ Database is completely empty');
    } else {
      console.log(`⚠️  Database still contains ${totalRows} total rows`);
    }

    console.log('🎉 Database flush completed successfully!');
    
  } catch (error) {
    console.error('❌ Error flushing database:', error);
    throw error;
  } finally {
    await db.end();
  }
};

// Run the flush
flushDatabase()
  .then(() => {
    console.log('✅ Database flush finished');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Database flush failed:', error);
    process.exit(1);
  });
