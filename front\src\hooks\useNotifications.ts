import { useSettings } from '../contexts/SettingsContext';

/**
 * Custom hook for accessing notification settings
 * Provides easy access to notification preferences and helper functions
 */
export const useNotifications = () => {
  const { getNotificationSettings } = useSettings();
  const notificationSettings = getNotificationSettings();

  // Helper functions to check if specific notification types are enabled
  const isEmailNotificationsEnabled = () => {
    return notificationSettings?.enableEmailNotifications ?? true;
  };

  const isOrderConfirmationsEnabled = () => {
    return notificationSettings?.enableOrderConfirmations ?? true;
  };

  const isVoucherNotificationsEnabled = () => {
    return notificationSettings?.enableVoucherNotifications ?? true;
  };

  const isSecurityAlertsEnabled = () => {
    return notificationSettings?.enableSecurityAlerts ?? true;
  };

  // Helper function to check if a specific notification type should be shown in UI
  const shouldShowNotificationType = (type: 'email' | 'order' | 'voucher' | 'security') => {
    if (!isEmailNotificationsEnabled()) {
      return false; // If global email notifications are disabled, don't show any
    }

    switch (type) {
      case 'email':
        return isEmailNotificationsEnabled();
      case 'order':
        return isOrderConfirmationsEnabled();
      case 'voucher':
        return isVoucherNotificationsEnabled();
      case 'security':
        return isSecurityAlertsEnabled();
      default:
        return false;
    }
  };

  return {
    notificationSettings,
    isEmailNotificationsEnabled,
    isOrderConfirmationsEnabled,
    isVoucherNotificationsEnabled,
    isSecurityAlertsEnabled,
    shouldShowNotificationType,
  };
};
