import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { executeQuery, executeTransaction } from '../config/database';
import { logger, logPaymentEvent } from '../config/logger';
import { verifyTransaction } from '../services/tonService';
import { generateVoucher } from './voucherController';
import { sendEmail } from '../services/emailService';

// TON payment webhook handler
export const handleTonPaymentWebhook = async (req: Request, res: Response) => {
  try {
    const { 
      transactionHash, 
      fromAddress, 
      toAddress, 
      amount, 
      memo,
      timestamp 
    } = req.body;

    // Basic validation
    if (!transactionHash || !fromAddress || !toAddress || !amount) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
      });
    }

    logger.info('Received TON payment webhook', {
      transactionHash,
      fromAddress,
      toAddress,
      amount,
      memo,
    });

    // Check if transaction already processed
    const existingTransaction = await executeQuery(
      'SELECT id, order_id FROM transactions WHERE hash = $1',
      [transactionHash]
    );

    if (existingTransaction.rows.length > 0) {
      logger.info('Transaction already processed', { transactionHash });
      return res.json({
        success: true,
        message: 'Transaction already processed',
      });
    }

    // Find order by payment address
    const orderResult = await executeQuery(
      'SELECT id, user_id, status, amount as order_amount, currency FROM orders WHERE payment_address = $1 AND status IN ($2, $3)',
      [toAddress, 'pending', 'payment_pending']
    );

    if (orderResult.rows.length === 0) {
      logger.warn('No pending order found for payment address', { toAddress });
      return res.status(404).json({
        success: false,
        error: 'No pending order found for this payment address',
      });
    }

    const order = orderResult.rows[0];

    // Verify transaction on blockchain
    const verificationResult = await verifyTransaction(
      transactionHash,
      amount,
      toAddress,
      memo
    );

    if (!verificationResult.isValid) {
      logger.error('Transaction verification failed', {
        transactionHash,
        error: verificationResult.error,
      });
      return res.status(400).json({
        success: false,
        error: verificationResult.error || 'Transaction verification failed',
      });
    }

    // Process payment in transaction
    const queries = [
      {
        text: `UPDATE orders 
               SET status = $1, transaction_hash = $2, updated_at = CURRENT_TIMESTAMP 
               WHERE id = $3`,
        params: ['paid', transactionHash, order.id],
      },
      {
        text: `INSERT INTO transactions (id, order_id, hash, from_address, to_address, amount, status, confirmations, created_at)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)`,
        params: [
          uuidv4(),
          order.id,
          transactionHash,
          fromAddress,
          toAddress,
          amount,
          'confirmed',
          1,
        ],
      },
    ];

    await executeTransaction(queries);

    // Generate voucher for the order
    try {
      await generateVoucher(order.id, order.user_id);
      
      // Update order status to completed
      await executeQuery(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['completed', order.id]
      );

      // Send payment confirmation email
      const userResult = await executeQuery(
        'SELECT email, telegram_id FROM users WHERE id = $1',
        [order.user_id]
      );

      if (userResult.rows.length > 0) {
        const user = userResult.rows[0];
        try {
          await sendEmail({
            to: user.email,
            subject: 'Payment Confirmed - TON Voucher Platform',
            template: 'payment-confirmation',
            data: {
              userName: user.telegram_id,
              orderDetails: {
                orderId: order.id,
                amount: order.order_amount,
                currency: order.currency,
              },
              transactionHash,
              voucherGenerated: true,
            },
          });
        } catch (emailError) {
          logger.error('Failed to send payment confirmation email:', emailError);
        }
      }
    } catch (voucherError) {
      logger.error('Failed to generate voucher after payment:', voucherError);
      // Keep order as paid, voucher can be generated manually
    }

    logPaymentEvent('PAYMENT_WEBHOOK_PROCESSED', order.id, amount, {
      userId: order.user_id,
      transactionHash,
      fromAddress,
    });

    res.json({
      success: true,
      message: 'Payment processed successfully',
      data: {
        orderId: order.id,
        transactionHash,
        status: 'completed',
      },
    });
  } catch (error) {
    logger.error('Webhook processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process webhook',
    });
  }
};

// Manual transaction verification endpoint (admin only)
export const verifyTransactionManually = async (req: Request, res: Response) => {
  try {
    const { transactionHash, orderId } = req.body;
    const adminId = req.user!.id;

    // Get order details
    const orderResult = await executeQuery(
      'SELECT id, user_id, status, amount, currency, payment_address FROM orders WHERE id = $1',
      [orderId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
      });
    }

    const order = orderResult.rows[0];

    // Check if order can be verified
    if (!['pending', 'payment_pending'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        error: `Order is ${order.status} and cannot be verified`,
      });
    }

    // Verify transaction on blockchain
    const verificationResult = await verifyTransaction(
      transactionHash,
      order.amount,
      order.payment_address
    );

    if (!verificationResult.isValid) {
      return res.status(400).json({
        success: false,
        error: verificationResult.error || 'Transaction verification failed',
      });
    }

    // Update order and create transaction record
    const queries = [
      {
        text: `UPDATE orders 
               SET status = $1, transaction_hash = $2, updated_at = CURRENT_TIMESTAMP 
               WHERE id = $3`,
        params: ['paid', transactionHash, orderId],
      },
      {
        text: `INSERT INTO transactions (id, order_id, hash, to_address, amount, status, confirmations, verified_by_admin, created_at)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)`,
        params: [
          uuidv4(),
          orderId,
          transactionHash,
          order.payment_address,
          order.amount,
          'confirmed',
          1,
          adminId,
        ],
      },
    ];

    await executeTransaction(queries);

    // Generate voucher
    try {
      await generateVoucher(orderId, order.user_id);
      
      // Update order status to completed
      await executeQuery(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['completed', orderId]
      );
    } catch (voucherError) {
      logger.error('Failed to generate voucher after manual verification:', voucherError);
    }

    logPaymentEvent('PAYMENT_MANUALLY_VERIFIED', orderId, order.amount, {
      userId: order.user_id,
      adminId,
      transactionHash,
    });

    res.json({
      success: true,
      message: 'Transaction verified and processed successfully',
      data: {
        orderId,
        transactionHash,
        status: 'completed',
      },
    });
  } catch (error) {
    logger.error('Manual verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify transaction',
    });
  }
};

// Get webhook logs (admin only)
export const getWebhookLogs = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;

    // Get webhook logs from audit table or create a dedicated webhook_logs table
    const logsResult = await executeQuery(
      `SELECT id, action, resource_type, resource_id, details, ip_address, user_agent, created_at
       FROM audit_logs 
       WHERE action LIKE '%WEBHOOK%' OR action LIKE '%PAYMENT%'
       ORDER BY created_at DESC 
       LIMIT $1 OFFSET $2`,
      [limit, offset]
    );

    const countResult = await executeQuery(
      `SELECT COUNT(*) 
       FROM audit_logs 
       WHERE action LIKE '%WEBHOOK%' OR action LIKE '%PAYMENT%'`
    );

    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        logs: logsResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get webhook logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get webhook logs',
    });
  }
};
