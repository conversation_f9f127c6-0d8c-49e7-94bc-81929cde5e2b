# Production Environment Configuration
# SECURITY WARNING: Never commit actual production values to version control
# Use secure secret management systems in production

NODE_ENV=production
PORT=3001

# Database Configuration - Production
DATABASE_URL=postgresql://tonsite_user:<EMAIL>:5432/tonsite_prod
DATABASE_HOST=db.yourcompany.com
DATABASE_PORT=5432
DATABASE_NAME=tonsite_prod
DATABASE_USER=tonsite_user
DATABASE_PASSWORD=SECURE_DB_PASSWORD_HERE
DATABASE_SSL=true

# Redis Configuration - Production
REDIS_URL=redis://:<EMAIL>:6379
REDIS_HOST=redis.yourcompany.com
REDIS_PORT=6379
REDIS_PASSWORD=SECURE_REDIS_PASSWORD_HERE
REDIS_DB=0

# JWT Configuration - Production (Use strong secrets)
JWT_SECRET=GENERATE_SECURE_JWT_SECRET_MIN_64_CHARS_HERE
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration - Production
SESSION_SECRET=GENERATE_SECURE_SESSION_SECRET_MIN_64_CHARS_HERE
SESSION_MAX_AGE=86400000

# CSRF Configuration - Production
CSRF_SECRET=GENERATE_SECURE_CSRF_SECRET_MIN_64_CHARS_HERE

# TON Configuration - Production
TON_NETWORK=mainnet
TON_API_KEY=PRODUCTION_TON_API_KEY
TON_ENDPOINT=https://toncenter.com/api/v2/jsonRPC
TON_WALLET_MNEMONIC=SECURE_PRODUCTION_WALLET_MNEMONIC_WORDS
TON_MASTER_WALLET_ADDRESS=PRODUCTION_MASTER_WALLET_ADDRESS

# Email Configuration - Production
EMAIL_SMTP_HOST=smtp.yourprovider.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASS=SECURE_EMAIL_APP_PASSWORD
EMAIL_FROM_NAME=Your Company Name
EMAIL_FROM_ADDRESS=<EMAIL>

# Security Configuration - Production
BCRYPT_ROUNDS=14
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=3600000
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=50

# CORS Configuration - Production
CORS_ORIGIN=https://yourapp.com,https://www.yourapp.com
CORS_CREDENTIALS=true

# File Upload Configuration - Production
UPLOAD_MAX_SIZE=5242880
UPLOAD_PATH=/secure/uploads

# Logging Configuration - Production
LOG_LEVEL=warn
LOG_FILE=/var/log/tonsite/app.log

# Admin Configuration - Production (CRITICAL: Change these!)
ADMIN_EMAIL=<EMAIL>
ADMIN_TELEGRAM_ID=your_admin_telegram
ADMIN_PASSWORD=GENERATE_VERY_SECURE_ADMIN_PASSWORD_HERE
ADMIN_SETUP_REQUIRED=true

# Business Rules - Production
VOUCHER_EXPIRY_DAYS=365
ORDER_TIMEOUT_MINUTES=30
PAYMENT_TIMEOUT_MINUTES=30
MIN_ORDER_AMOUNT=0.01
MAX_ORDER_AMOUNT=1000000

# Monitoring and Health - Production
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# Production Security Features
ENABLE_SWAGGER=false
ENABLE_CORS_DEBUG=false
FORCE_HTTPS=true
TRUST_PROXY=true

# Encryption Configuration - Production
ENCRYPTION_KEY=GENERATE_32_CHAR_ENCRYPTION_KEY_HERE

# Backup Configuration - Production
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1

# Monitoring Integration - Production
SENTRY_DSN=https://<EMAIL>/project-id
DATADOG_API_KEY=your-datadog-api-key
NEW_RELIC_LICENSE_KEY=your-newrelic-license-key

# SSL/TLS Configuration - Production
SSL_CERT_PATH=/etc/ssl/certs/yourapp.crt
SSL_KEY_PATH=/etc/ssl/private/yourapp.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# Rate Limiting - Production (Stricter)
AUTH_RATE_LIMIT_MAX=3
ORDER_RATE_LIMIT_MAX=5
PAYMENT_RATE_LIMIT_MAX=2

# Session Security - Production
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Additional Security Headers - Production
HSTS_MAX_AGE=31536000
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'
