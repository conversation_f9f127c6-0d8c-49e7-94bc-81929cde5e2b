import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { executeQuery, connectDatabases } from '../config/database';
import { logger } from '../config/logger';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';

// Load environment variables
dotenv.config();

const setupTestData = async () => {
  try {
    logger.info('Setting up test data...');

    // Connect to database
    await connectDatabases();

    // Run migration for products table
    logger.info('Running products migration...');
    const migrationPath = path.join(__dirname, '../../../database/migrations/002_add_products_table.sql');
    
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await executeQuery(migrationSQL);
      logger.info('Products migration completed successfully');
    } else {
      logger.warn('Products migration file not found, skipping...');
    }

    // Create test user if not exists
    logger.info('Creating test user...');
    const testEmail = '<EMAIL>';
    const testTelegramId = 'test_user_123';
    const testPassword = 'TestPassword123!';
    
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = $1',
      [testEmail]
    );

    let testUserId: string;
    
    if (existingUser.rows.length === 0) {
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      const userResult = await executeQuery(
        `INSERT INTO users (id, email, telegram_id, password_hash, email_verified, role)
         VALUES ($1, $2, $3, $4, true, 'user')
         RETURNING id`,
        [uuidv4(), testEmail, testTelegramId, hashedPassword]
      );
      testUserId = userResult.rows[0].id;
      logger.info(`Test user created with ID: ${testUserId}`);
    } else {
      testUserId = existingUser.rows[0].id;
      logger.info(`Test user already exists with ID: ${testUserId}`);
    }

    // Create test orders
    logger.info('Creating test orders...');
    
    const testOrders = [
      {
        id: uuidv4(),
        productId: 'steam',
        amount: 25.00,
        status: 'completed',
        memo: 'Steam gift card for gaming',
        quantity: 1,
      },
      {
        id: uuidv4(),
        productId: 'netflix',
        amount: 15.00,
        status: 'completed',
        memo: 'Netflix subscription gift',
        quantity: 1,
      },
      {
        id: uuidv4(),
        productId: 'amazon',
        amount: 50.00,
        status: 'pending',
        memo: 'Amazon shopping voucher',
        quantity: 1,
      },
      {
        id: uuidv4(),
        productId: 'spotify',
        amount: 10.00,
        status: 'payment_pending',
        memo: 'Spotify Premium subscription',
        quantity: 1,
      },
    ];

    for (const order of testOrders) {
      // Check if order already exists
      const existingOrder = await executeQuery(
        'SELECT id FROM orders WHERE id = $1',
        [order.id]
      );

      if (existingOrder.rows.length === 0) {
        await executeQuery(
          `INSERT INTO orders (id, user_id, status, amount, currency, memo, product_id, quantity, payment_address, created_at, updated_at)
           VALUES ($1, $2, $3, $4, 'TON', $5, $6, $7, $8, NOW() - INTERVAL '${Math.floor(Math.random() * 30)} days', NOW())`,
          [
            order.id,
            testUserId,
            order.status,
            order.amount,
            order.memo,
            order.productId,
            order.quantity,
            `EQD${Math.random().toString(36).substring(2, 15)}`, // Mock TON address
          ]
        );
        logger.info(`Created test order: ${order.productId} - ${order.status}`);
      }
    }

    // Create test vouchers for completed orders
    logger.info('Creating test vouchers...');
    
    const completedOrders = await executeQuery(
      'SELECT id, product_id, amount FROM orders WHERE user_id = $1 AND status = $2',
      [testUserId, 'completed']
    );

    for (const order of completedOrders.rows) {
      // Check if voucher already exists
      const existingVoucher = await executeQuery(
        'SELECT id FROM vouchers WHERE order_id = $1',
        [order.id]
      );

      if (existingVoucher.rows.length === 0) {
        const voucherCode = `${order.product_id.toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
        const expiresAt = new Date();
        expiresAt.setFullYear(expiresAt.getFullYear() + 1); // 1 year from now

        await executeQuery(
          `INSERT INTO vouchers (id, order_id, code, status, expires_at, created_at)
           VALUES ($1, $2, $3, 'active', $4, NOW() - INTERVAL '${Math.floor(Math.random() * 30)} days')`,
          [uuidv4(), order.id, voucherCode, expiresAt]
        );
        logger.info(`Created test voucher: ${voucherCode}`);
      }
    }

    // Create some redeemed vouchers for testing
    logger.info('Creating redeemed vouchers...');
    
    const additionalOrders = [
      {
        id: uuidv4(),
        productId: 'playstation',
        amount: 30.00,
        status: 'completed',
        memo: 'PlayStation Store gift card',
        quantity: 1,
      },
    ];

    for (const order of additionalOrders) {
      const existingOrder = await executeQuery(
        'SELECT id FROM orders WHERE id = $1',
        [order.id]
      );

      if (existingOrder.rows.length === 0) {
        await executeQuery(
          `INSERT INTO orders (id, user_id, status, amount, currency, memo, product_id, quantity, payment_address, created_at, updated_at)
           VALUES ($1, $2, $3, $4, 'TON', $5, $6, $7, $8, NOW() - INTERVAL '45 days', NOW())`,
          [
            order.id,
            testUserId,
            order.status,
            order.amount,
            order.memo,
            order.productId,
            order.quantity,
            `EQD${Math.random().toString(36).substring(2, 15)}`,
          ]
        );

        // Create redeemed voucher
        const voucherCode = `${order.productId.toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
        const redeemedAt = new Date();
        redeemedAt.setDate(redeemedAt.getDate() - 10); // Redeemed 10 days ago

        await executeQuery(
          `INSERT INTO vouchers (id, order_id, code, status, redeemed_at, redeemed_by_user_id, expires_at, created_at)
           VALUES ($1, $2, $3, 'redeemed', $4, $5, $6, NOW() - INTERVAL '45 days')`,
          [
            uuidv4(),
            order.id,
            voucherCode,
            redeemedAt,
            testUserId,
            new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          ]
        );
        logger.info(`Created redeemed voucher: ${voucherCode}`);
      }
    }

    logger.info('Test data setup completed successfully!');
    logger.info(`Test user credentials: ${testEmail} / ${testPassword}`);
    
  } catch (error) {
    logger.error('Error setting up test data:', error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  setupTestData()
    .then(() => {
      logger.info('Test data setup finished');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Test data setup failed:', error);
      process.exit(1);
    });
}

export { setupTestData };
