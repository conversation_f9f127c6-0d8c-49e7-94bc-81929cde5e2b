import { Request, Response } from 'express';
import { settingsService } from '../services/settingsService';
import { logger } from '../config/logger';

/**
 * SECURE PUBLIC SETTINGS API - ZERO-TRUST APPROACH
 * Only exposes absolute minimum settings required for frontend UI functionality
 * NO SENSITIVE DATA - NO INFRASTRUCTURE DETAILS - NO SECURITY CAPABILITIES
 */
export const getPublicSettings = async (req: Request, res: Response) => {
  try {
    // Get only the minimal settings required for frontend UI
    const paymentSettings = await settingsService.getPaymentSettings();
    const systemSettings = await settingsService.getSystemSettings();
    const voucherSettings = await settingsService.getVoucherSettings();
    const securitySettings = await settingsService.getSecuritySettings();

    // SECURE PUBLIC SETTINGS - MINIMAL EXPOSURE ONLY
    const publicSettings = {
      payment: {
        // Only currency options and basic limits for UI validation
        supportedCurrencies: paymentSettings.supportedCurrencies,
        minOrderAmount: paymentSettings.minOrderAmount,
        maxOrderAmount: paymentSettings.maxOrderAmount,
        paymentTimeoutMinutes: paymentSettings.paymentTimeoutMinutes,
      },
      voucher: {
        // Only UI limits for form validation
        defaultExpiryDays: voucherSettings.defaultExpiryDays,
        maxVouchersPerOrder: voucherSettings.maxVouchersPerOrder,
        allowVoucherStacking: voucherSettings.allowVoucherStacking,
      },
      security: {
        // ONLY password requirements for client-side validation
        // NO OTHER SECURITY SETTINGS EXPOSED
        passwordMinLength: securitySettings.passwordMinLength,
        passwordRequireLowercase: securitySettings.passwordRequireLowercase,
        passwordRequireNumbers: securitySettings.passwordRequireNumbers,
        passwordRequireSymbols: securitySettings.passwordRequireSymbols,
        passwordRequireUppercase: securitySettings.passwordRequireUppercase,
      },
      system: {
        // Only maintenance mode for UI display
        maintenanceMode: systemSettings.maintenanceMode,
        maintenanceMessage: systemSettings.maintenanceMessage,
        // REMOVED: apiVersion, maxFileUploadSize (security risk)
      },
    };

    // SECURITY: Log all public settings access for monitoring
    logger.info('Public settings accessed', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      settingsRequested: Object.keys(publicSettings)
    });

    res.json({
      success: true,
      data: { settings: publicSettings },
    });
  } catch (error) {
    logger.error('Get public settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get public settings',
    });
  }
};

/**
 * SECURE CATEGORY-SPECIFIC PUBLIC SETTINGS - RESTRICTED ACCESS
 * Only allows access to specific safe categories with minimal data exposure
 */
export const getPublicSettingsByCategory = async (req: Request, res: Response) => {
  try {
    const { category } = req.params;

    let settings;

    switch (category) {
      case 'payment':
        settings = await settingsService.getPaymentSettings();
        // Only expose non-sensitive payment settings
        settings = {
          supportedCurrencies: settings.supportedCurrencies,
          minOrderAmount: settings.minOrderAmount,
          maxOrderAmount: settings.maxOrderAmount,
          paymentTimeoutMinutes: settings.paymentTimeoutMinutes,
        };
        break;

      case 'voucher':
        settings = await settingsService.getVoucherSettings();
        // Only basic voucher settings for UI validation
        settings = {
          defaultExpiryDays: settings.defaultExpiryDays,
          maxVouchersPerOrder: settings.maxVouchersPerOrder,
          allowVoucherStacking: settings.allowVoucherStacking,
        };
        break;

      case 'security':
        const securitySettingsAll = await settingsService.getSecuritySettings();
        // ONLY password requirements for frontend validation
        settings = {
          passwordMinLength: securitySettingsAll.passwordMinLength,
          passwordRequireLowercase: securitySettingsAll.passwordRequireLowercase,
          passwordRequireNumbers: securitySettingsAll.passwordRequireNumbers,
          passwordRequireSymbols: securitySettingsAll.passwordRequireSymbols,
          passwordRequireUppercase: securitySettingsAll.passwordRequireUppercase,
        };
        break;

      case 'system':
        const systemSettings = await settingsService.getSystemSettings();
        // Only maintenance mode for UI display
        settings = {
          maintenanceMode: systemSettings.maintenanceMode,
          maintenanceMessage: systemSettings.maintenanceMessage,
        };
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid settings category. Allowed: payment, voucher, security, system',
        });
    }

    logger.debug(`Public settings requested for category: ${category}`, { 
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      category
    });

    res.json({
      success: true,
      data: { settings },
    });
  } catch (error) {
    logger.error(`Get public settings by category error (${req.params.category}):`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to get public settings',
    });
  }
};
