'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
  requireEmailVerification?: boolean;
  requireRole?: 'user' | 'admin';
  redirectTo?: string;
}

export default function ProtectedRoute({
  children,
  requireEmailVerification = false,
  requireRole,
  redirectTo = '/auth/login',
}: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      // Not authenticated
      if (!user) {
        router.push(redirectTo);
        return;
      }

      // Email verification required but not verified
      if (requireEmailVerification && !user.emailVerified) {
        router.push('/auth/verify-email');
        return;
      }

      // Role requirement not met
      if (requireRole && user.role !== requireRole) {
        router.push('/dashboard');
        return;
      }
    }
  }, [user, loading, router, requireEmailVerification, requireRole, redirectTo]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner-lg mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (!user) {
    return null;
  }

  // Email verification required but not verified
  if (requireEmailVerification && !user.emailVerified) {
    return null;
  }

  // Role requirement not met
  if (requireRole && user.role !== requireRole) {
    return null;
  }

  // All checks passed, render children
  return <>{children}</>;
}
