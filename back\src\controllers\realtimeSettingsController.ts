import { Request, Response } from 'express';
import { settingsService } from '../services/settingsService';
import { logger } from '../config/logger';
import { WebSocketServer } from 'ws';
import jwt from 'jsonwebtoken';
import { executeQuery } from '../config/database';
import { env } from '../config/env-validation';

/**
 * SECURE REAL-TIME SETTINGS CONTROLLER
 * Provides authenticated access to settings with real-time updates via WebSocket
 */

// WebSocket server instance (will be initialized in app.ts)
let wss: WebSocketServer | null = null;

export const setWebSocketServer = (webSocketServer: WebSocketServer) => {
  wss = webSocketServer;
};

/**
 * Get authenticated settings for frontend (non-sensitive data only)
 * This replaces the vulnerable public settings API for authenticated users
 */
export const getAuthenticatedSettings = async (req: Request, res: Response) => {
  try {
    // Get settings that authenticated users need for UI functionality
    const paymentSettings = await settingsService.getPaymentSettings();
    const systemSettings = await settingsService.getSystemSettings();
    const voucherSettings = await settingsService.getVoucherSettings();
    const securitySettings = await settingsService.getSecuritySettings();
    const notificationSettings = await settingsService.getNotificationSettings();

    // AUTHENTICATED SETTINGS - More data than public but still security-conscious
    const authenticatedSettings = {
      payment: {
        supportedCurrencies: paymentSettings.supportedCurrencies,
        minOrderAmount: paymentSettings.minOrderAmount,
        maxOrderAmount: paymentSettings.maxOrderAmount,
        paymentTimeoutMinutes: paymentSettings.paymentTimeoutMinutes,
        // No additional payment settings in current schema
      },
      voucher: {
        defaultExpiryDays: voucherSettings.defaultExpiryDays,
        maxVouchersPerOrder: voucherSettings.maxVouchersPerOrder,
        allowVoucherStacking: voucherSettings.allowVoucherStacking,
        // No additional voucher settings in current schema
      },
      security: {
        // Password requirements for client-side validation
        passwordMinLength: securitySettings.passwordMinLength,
        passwordRequireLowercase: securitySettings.passwordRequireLowercase,
        passwordRequireNumbers: securitySettings.passwordRequireNumbers,
        passwordRequireSymbols: securitySettings.passwordRequireSymbols,
        passwordRequireUppercase: securitySettings.passwordRequireUppercase,
        enableTwoFactor: securitySettings.enableTwoFactor,
        // Additional security settings for authenticated users
        maxLoginAttempts: securitySettings.maxLoginAttempts,
        lockoutDurationMinutes: securitySettings.lockoutDurationMinutes,
      },
      system: {
        maintenanceMode: systemSettings.maintenanceMode,
        maintenanceMessage: systemSettings.maintenanceMessage,
        // Additional system settings for authenticated users
        apiVersion: systemSettings.apiVersion,
        maxFileUploadSize: systemSettings.maxFileUploadSize,
        enableAnalytics: systemSettings.enableAnalytics,
        logLevel: systemSettings.logLevel,
      },
      notifications: {
        enableEmailNotifications: notificationSettings.enableEmailNotifications,
        enableOrderConfirmations: notificationSettings.enableOrderConfirmations,
        enableVoucherNotifications: notificationSettings.enableVoucherNotifications,
        enableSecurityAlerts: notificationSettings.enableSecurityAlerts,
        // No additional notification settings in current schema
      },
    };

    // Log authenticated settings access
    logger.info('Authenticated settings accessed', {
      userId: req.user?.id,
      userRole: req.user?.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
    });

    res.json({
      success: true,
      data: { settings: authenticatedSettings },
    });
  } catch (error) {
    logger.error('Get authenticated settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get authenticated settings',
    });
  }
};

/**
 * Broadcast settings update to all connected admin clients via WebSocket
 */
export const broadcastSettingsUpdate = async (category: string, settingKey: string, newValue: any) => {
  if (!wss) {
    logger.warn('WebSocket server not initialized, cannot broadcast settings update');
    return;
  }

  const updateMessage = {
    type: 'SETTINGS_UPDATE',
    data: {
      category,
      settingKey,
      newValue,
      timestamp: new Date().toISOString(),
    },
  };

  // Broadcast to all connected admin clients
  wss.clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      try {
        client.send(JSON.stringify(updateMessage));
      } catch (error) {
        logger.error('Failed to send WebSocket message to client:', error);
      }
    }
  });

  logger.info('Settings update broadcasted', {
    category,
    settingKey,
    connectedClients: wss.clients.size,
  });
};

/**
 * WebSocket authentication middleware
 */
export const authenticateWebSocket = async (token: string): Promise<any> => {
  try {
    if (!token) {
      throw new Error('No token provided');
    }

    const payload = jwt.verify(token, env.JWT_SECRET) as any;
    if (!payload || !payload.userId) {
      throw new Error('Invalid token');
    }

    // Get user from database
    const userResult = await executeQuery(
      'SELECT id, email, role FROM users WHERE id = $1 AND role = $2',
      [payload.userId, 'admin']
    );

    if (userResult.rows.length === 0) {
      throw new Error('Admin user not found');
    }

    return userResult.rows[0];
  } catch (error) {
    logger.error('WebSocket authentication failed:', error);
    throw error;
  }
};

/**
 * Initialize WebSocket server for real-time settings updates
 */
export const initializeWebSocketServer = (server: any) => {
  const webSocketServer = new WebSocketServer({ 
    server,
    path: '/ws/admin/settings'
  });

  webSocketServer.on('connection', async (ws, req) => {
    try {
      // Extract token from query parameters or headers
      const url = new URL(req.url!, `http://${req.headers.host}`);
      const token = url.searchParams.get('token') || req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        ws.close(1008, 'Authentication required');
        return;
      }

      // Authenticate the WebSocket connection
      const user = await authenticateWebSocket(token);
      
      // Store user info on the WebSocket connection
      (ws as any).user = user;

      logger.info('Admin WebSocket connected', {
        userId: user.id,
        userEmail: user.email,
        ip: req.socket.remoteAddress,
      });

      // Send initial connection confirmation
      ws.send(JSON.stringify({
        type: 'CONNECTION_ESTABLISHED',
        data: {
          message: 'Real-time settings updates enabled',
          timestamp: new Date().toISOString(),
        },
      }));

      // Handle WebSocket disconnection
      ws.on('close', () => {
        logger.info('Admin WebSocket disconnected', {
          userId: user.id,
          userEmail: user.email,
        });
      });

      // Handle WebSocket errors
      ws.on('error', (error) => {
        logger.error('WebSocket error:', error);
      });

    } catch (error) {
      logger.error('WebSocket connection failed:', error);
      ws.close(1008, 'Authentication failed');
    }
  });

  setWebSocketServer(webSocketServer);
  logger.info('WebSocket server initialized for real-time settings updates');

  return webSocketServer;
};
