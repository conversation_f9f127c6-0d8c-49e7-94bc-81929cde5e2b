'use client';

import { useState, useEffect } from 'react';
import { adminApi } from '@/lib/api';
import { 
  Users, 
  ShoppingBag, 
  Ticket, 
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock
} from 'lucide-react';

interface AdminStats {
  totalUsers: number;
  verifiedUsers: number;
  totalOrders: number;
  completedOrders: number;
  totalVouchers: number;
  activeVouchers: number;
  totalRevenue: number;
  ordersToday: number;
  usersToday: number;
}

interface RecentOrder {
  id: string;
  amount: string;
  currency: string;
  status: string;
  created_at: string;
  telegram_id: string;
}

interface RecentUser {
  id: string;
  email: string;
  telegram_id: string;
  email_verified: boolean;
  role: string;
  created_at: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [recentUsers, setRecentUsers] = useState<RecentUser[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getStats();
      
      if (response.success && response.data) {
        setStats(response.data.stats);
        setRecentOrders(response.data.recentOrders || []);
        setRecentUsers(response.data.recentUsers || []);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-success-600 bg-success-50';
      case 'pending':
        return 'text-warning-600 bg-warning-50';
      case 'failed':
        return 'text-error-600 bg-error-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="spinner-lg mx-auto mb-4" />
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600">Overview of your TON voucher platform</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {/* Total Users */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-6 w-6 text-ton-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.totalUsers || 0}</dd>
                </dl>
              </div>
            </div>
            <div className="mt-3">
              <div className="flex items-center text-sm">
                <span className="text-success-600">+{stats?.usersToday || 0}</span>
                <span className="text-gray-500 ml-1">today</span>
              </div>
            </div>
          </div>
        </div>

        {/* Total Orders */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShoppingBag className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.totalOrders || 0}</dd>
                </dl>
              </div>
            </div>
            <div className="mt-3">
              <div className="flex items-center text-sm">
                <span className="text-success-600">+{stats?.ordersToday || 0}</span>
                <span className="text-gray-500 ml-1">today</span>
              </div>
            </div>
          </div>
        </div>

        {/* Total Vouchers */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Ticket className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Vouchers</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.activeVouchers || 0}</dd>
                </dl>
              </div>
            </div>
            <div className="mt-3">
              <div className="flex items-center text-sm">
                <span className="text-gray-500">of {stats?.totalVouchers || 0} total</span>
              </div>
            </div>
          </div>
        </div>

        {/* Total Revenue */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats?.totalRevenue ? `${stats.totalRevenue} TON` : '0 TON'}
                  </dd>
                </dl>
              </div>
            </div>
            <div className="mt-3">
              <div className="flex items-center text-sm">
                <span className="text-gray-500">
                  {stats?.completedOrders || 0} completed orders
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Orders */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Orders</h3>
          </div>
          <div className="card-body p-0">
            <div className="flow-root">
              <ul className="divide-y divide-gray-200">
                {recentOrders.length > 0 ? (
                  recentOrders.map((order) => (
                    <li key={order.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <ShoppingBag className="h-5 w-5 text-gray-400" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">
                              {order.amount} {order.currency}
                            </p>
                            <p className="text-sm text-gray-500">
                              by @{order.telegram_id}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                          <span className="ml-2 text-xs text-gray-500">
                            {new Date(order.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="px-6 py-4 text-center text-gray-500">
                    No recent orders
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* Recent Users */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Users</h3>
          </div>
          <div className="card-body p-0">
            <div className="flow-root">
              <ul className="divide-y divide-gray-200">
                {recentUsers.length > 0 ? (
                  recentUsers.map((user) => (
                    <li key={user.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                              <Users className="h-4 w-4 text-gray-500" />
                            </div>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">
                              {user.email}
                            </p>
                            <p className="text-sm text-gray-500">
                              @{user.telegram_id}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {user.email_verified ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-success-600 bg-success-50">
                              Verified
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-warning-600 bg-warning-50">
                              Pending
                            </span>
                          )}
                          <span className="ml-2 text-xs text-gray-500">
                            {new Date(user.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="px-6 py-4 text-center text-gray-500">
                    No recent users
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
